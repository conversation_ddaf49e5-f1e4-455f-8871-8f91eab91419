import os
import requests
from dotenv import load_dotenv
import logging

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

def read_sql_file(filename):
    with open(filename, 'r') as file:
        return file.read()

def execute_sql(sql_command):
    url = f"{os.getenv('SUPABASE_URL')}/rest/v1/rpc/exec_sql"
    headers = {
        'apikey': os.getenv('SUPABASE_KEY'),
        'Authorization': f"Bearer {os.getenv('SUPABASE_KEY')}",
        'Content-Type': 'application/json',
        'Prefer': 'return=minimal'
    }
    data = {'sql_command': sql_command}
    
    response = requests.post(url, headers=headers, json=data)
    if response.status_code != 200:
        raise Exception(f"Error executing SQL: {response.text}")

def verify_view(view_name):
    url = f"{os.getenv('SUPABASE_URL')}/rest/v1/{view_name}"
    headers = {
        'apikey': os.getenv('SUPABASE_KEY'),
        'Authorization': f"Bearer {os.getenv('SUPABASE_KEY')}",
        'Range': '0-0'
    }
    
    response = requests.get(url, headers=headers)
    if response.status_code == 200:
        total_count = response.headers.get('content-range', '').split('/')[1]
        return int(total_count) if total_count else 0
    else:
        raise Exception(f"Error verifying view: {response.text}")

def apply_views():
    try:
        logger.info("Starting to apply views...")
        
        # Read the SQL file
        sql_commands = read_sql_file('create_option_views.sql')
        
        # Split commands and execute them
        commands = sql_commands.split(';')
        for command in commands:
            command = command.strip()
            if command:
                try:
                    execute_sql(command)
                    logger.info(f"Successfully executed command")
                except Exception as e:
                    logger.error(f"Error executing command: {e}")
        
        # Verify views were created
        logger.info("\nVerifying views...")
        views = [
            'option_chain_detailed_view',
            'option_chain_changes_view',
            'option_chain_summary_view',
            'intraday_changes_view'
        ]
        
        for view in views:
            try:
                count = verify_view(view)
                logger.info(f"View {view} created successfully with {count} records")
            except Exception as e:
                logger.error(f"Error verifying view {view}: {e}")
        
        logger.info("\nAll views have been applied successfully!")
        
    except Exception as e:
        logger.error(f"Error applying views: {e}")
        raise

if __name__ == "__main__":
    apply_views() 