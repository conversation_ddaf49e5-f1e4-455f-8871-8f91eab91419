import os
import json
import requests
import pandas as pd
from dotenv import load_dotenv
from datetime import datetime, timedelta
import time
import schedule
import signal
import sys
import logging
import pytz
from dhanhq import dhanhq
from supabase import create_client, Client
from dateutil.relativedelta import relativedelta
import re
from config import (
    ASSETS, TIME_CONFIG, DB_CONFIG, LOG_CONFIG, 
    ERROR_CONFIG, RATE_LIMIT_CONFIG, is_trading_hours,
    DEFAULT_ASSET
)

# Load environment variables from .env file
load_dotenv()

# Setup logging
logging.basicConfig(
    level=getattr(logging, LOG_CONFIG['log_level']),
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(LOG_CONFIG['log_file']) if LOG_CONFIG['log_to_file'] else logging.NullHandler()
    ]
)
logger = logging.getLogger(__name__)

# DhanHQ credentials from environment variables
ACCESS_TOKEN = os.getenv('DHAN_ACCESS_TOKEN')
CLIENT_ID = os.getenv('DHAN_CLIENT_ID')

# Supabase credentials
SUPABASE_URL = os.getenv('SUPABASE_URL')
SUPABASE_KEY = os.getenv('SUPABASE_KEY')

# Validate credentials
if not ACCESS_TOKEN or not CLIENT_ID:
    raise ValueError("Please set DHAN_ACCESS_TOKEN and DHAN_CLIENT_ID in your .env file")

if not SUPABASE_URL or not SUPABASE_KEY:
    raise ValueError("Please set SUPABASE_URL and SUPABASE_KEY in your .env file")

# Initialize DhanHQ client
dhan = dhanhq(
    client_id=CLIENT_ID,
    access_token=ACCESS_TOKEN
)

# Initialize Supabase client
supabase: Client = create_client(
    SUPABASE_URL,
    SUPABASE_KEY
)

logger.info("Successfully initialized DhanHQ and Supabase clients")

def get_expiry_dates():
    """Fetch all available expiry dates for the default asset"""
    try:
        asset = ASSETS[DEFAULT_ASSET]
        logger.info(f"Fetching expiry dates for {DEFAULT_ASSET} with security_id: {asset['security_id']}, exchange_segment: {asset['exchange_segment']}")
        
        response = dhan.expiry_list(
            under_security_id=asset['security_id'],
            under_exchange_segment=asset['exchange_segment']
        )
        
        # Log the raw response to help diagnose the issue
        logger.info(f"Raw expiry_list response: {json.dumps(response, indent=2)}")
        
        if (isinstance(response, dict) and 
            response.get('status') == 'success' and 
            isinstance(response.get('data'), dict) and
            isinstance(response['data'].get('data'), list)):
            
            expiry_dates = response['data']['data']
            logger.info(f"Found {len(expiry_dates)} expiry dates for {DEFAULT_ASSET}")
            return expiry_dates
        # Check for alternative response formats
        elif isinstance(response, dict) and response.get('status') == 'success' and isinstance(response.get('data'), list):
            # Handle case where data is directly a list
            expiry_dates = response['data']
            logger.info(f"Found {len(expiry_dates)} expiry dates for {DEFAULT_ASSET} (direct list format)")
            return expiry_dates
        elif isinstance(response, dict) and response.get('data') and isinstance(response.get('data'), dict):
            # Log all keys in the data object to understand structure
            logger.info(f"Data keys in response: {list(response['data'].keys())}")
            
            # Try to identify where expiry dates might be stored
            for key, value in response['data'].items():
                if isinstance(value, list) and value:
                    logger.info(f"Potential expiry dates found in key '{key}' with {len(value)} items")
                    return value
                    
            logger.error(f"Error fetching expiry dates: Found data object but no valid list of dates")
            return []
        else:
            logger.error(f"Error fetching expiry dates: Unexpected response format")
            if isinstance(response, dict):
                logger.error(f"Response keys: {list(response.keys())}")
                if 'status' in response:
                    logger.error(f"Status: {response['status']}")
                if 'message' in response:
                    logger.error(f"Message: {response['message']}")
                if 'error' in response:
                    logger.error(f"Error: {response['error']}")
            logger.debug(f"Response: {json.dumps(response, indent=2)}")
            return []
    except Exception as e:
        logger.error(f"Error fetching expiry dates: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return []

def process_options_data(options_chain, expiry_date):
    """Process options chain data and save to Supabase"""
    try:
        if not options_chain or not isinstance(options_chain, dict):
            logger.warning(f"No valid data available for expiry {expiry_date}")
            return
        
        # logger.info(f"Processing options data for expiry {expiry_date}")
        # logger.info(f"Options chain: {options_chain}")
        # The response has a nested structure
        if not (options_chain.get('status') == 'success' and 
                isinstance(options_chain.get('data'), dict) and
                isinstance(options_chain['data'].get('data'), dict)):
            logger.error(f"Invalid response format for expiry {expiry_date}")
            return

        options_data = options_chain['data']['data']
        spot_price = options_data.get('last_price', 0)
        timestamp = datetime.now(pytz.timezone('Asia/Kolkata')).isoformat()
        
        # Store complete raw response in nifty_options_raw table
        try:
            raw_record = {
                'expiry_date': expiry_date,
                'asset_name': DEFAULT_ASSET,
                'raw_data': options_chain,
                'spot_price': float(spot_price) if spot_price else 0
            }
            supabase.table('nifty_options_raw').insert(raw_record).execute()
            logger.info(f"Successfully stored complete options chain data for expiry {expiry_date}")
        except Exception as e:
            logger.error(f"Error storing raw options data: {e}")
        
        # Lists to store options data
        records = []
        
        # Process option chain data
        oc_data = options_data.get('oc', {})
        for strike_key, strike_data in oc_data.items():
            try:
                strike_price = float(strike_key.replace('.000000', ''))
            except (ValueError, AttributeError):
                continue
            
            # Process call and put options
            for option_type in ['ce', 'pe']:
                option_data = strike_data.get(option_type, {})
                if option_data:
                    option = {
                        'timestamp': timestamp,
                        'expiry_date': expiry_date,
                        'strike_price': strike_price,
                        'option_type': option_type.upper(),
                        'spot_price': float(spot_price) if spot_price else 0,
                        'last_price': float(option_data.get('last_price', 0) or 0),
                        'previous_close_price': float(option_data.get('previous_close_price', 0) or 0),
                        'volume': int(option_data.get('volume', 0) or 0),
                        'previous_volume': int(option_data.get('previous_volume', 0) or 0),
                        'oi': int(option_data.get('oi', 0) or 0),
                        'previous_oi': int(option_data.get('previous_oi', 0) or 0),
                        'top_bid_price': float(option_data.get('top_bid_price', 0) or 0),
                        'top_ask_price': float(option_data.get('top_ask_price', 0) or 0),
                        'top_bid_quantity': int(option_data.get('top_bid_quantity', 0) or 0),
                        'top_ask_quantity': int(option_data.get('top_ask_quantity', 0) or 0),
                        'iv': float(option_data.get('implied_volatility', 0) or 0)
                    } 
                    # Add greeks if available
                    greeks = option_data.get('greeks', {})
                    if greeks:
                        option.update({
                            'delta': float(greeks.get('delta', 0) or 0),
                            'gamma': float(greeks.get('gamma', 0) or 0),
                            'theta': float(greeks.get('theta', 0) or 0),
                            'vega': float(greeks.get('vega', 0) or 0)
                        })
                    else:
                        option.update({
                            'delta': 0.0,
                            'gamma': 0.0,
                            'theta': 0.0,
                            'vega': 0.0
                        })
                    
                    records.append(option)
        
        if records:
            try:
                # Save to Supabase in batches
                for i in range(0, len(records), DB_CONFIG['batch_size']):
                    batch = records[i:i + DB_CONFIG['batch_size']]
                    response = supabase.table('nifty').insert(batch).execute()
                    # logger.info(f"Response: {response}")
                    logger.info(f"Successfully stored batch of {len(batch)} records for expiry {expiry_date}")
                
                # Calculate ATM strike
                atm_strike = min(records, key=lambda x: abs(x['strike_price'] - spot_price))['strike_price']
                
                # Log summary
                logger.info(f"\nOptions Chain Summary for {expiry_date}:")
                logger.info(f"Spot Price: {spot_price}")
                logger.info(f"Total Strikes: {len(oc_data)}")
                logger.info(f"Total Options: {len(records)}")
                logger.info(f"ATM Strike: {atm_strike}")
                
            except Exception as e:
                logger.error(f"Error uploading to Supabase: {e}")
                # Save to CSV as backup
                df = pd.DataFrame(records)
                csv_file = f"option_data/nifty_options_{expiry_date.replace('-', '')}.csv"
                df.to_csv(csv_file, index=False)
                logger.info(f"Backup saved to {csv_file}")
        else:
            logger.warning(f"No valid options data found for {expiry_date}")
            
    except Exception as e:
        logger.error(f"Error processing options data for {expiry_date}: {e}")
        import traceback
        logger.error(traceback.format_exc())

def store_expiry_dates(expiry_dates):
    """Store expiry dates in the database"""
    try:
        # Convert expiry dates to records
        records = [
            {
                'expiry_date': date,
                'asset_name': DEFAULT_ASSET,
                'updated_at': datetime.now(pytz.timezone('Asia/Kolkata')).isoformat()
            }
            for date in expiry_dates
        ]
        
        # Upsert the records (insert if not exists, update if exists)
        response = supabase.table('expiry_dates').upsert(
            records,
            on_conflict='expiry_date,asset_name'  # Composite unique constraint
        ).execute()
        
        logger.info(f"Successfully stored/updated {len(records)} expiry dates")
        return True
    except Exception as e:
        logger.error(f"Error storing expiry dates: {e}")
        return False

def get_options_raw_data(expiry_date=None, start_date=None, end_date=None, limit=1):
    """
    Fetch raw options data from the database for analysis and charting
    
    Args:
        expiry_date (str, optional): Specific expiry date to query
        start_date (str, optional): Start date for time-range query (ISO format)
        end_date (str, optional): End date for time-range query (ISO format)
        limit (int, optional): Maximum number of records to return
        
    Returns:
        list: List of raw options data records
    """
    try:
        query = supabase.table('nifty_options_raw').select('*')
        
        # Apply filters if provided
        if expiry_date:
            query = query.eq('expiry_date', expiry_date)
        
        if start_date:
            query = query.gte('created_at', start_date)
            
        if end_date:
            query = query.lte('created_at', end_date)
        
        # Order by creation time (newest first)
        query = query.order('created_at', desc=True)
        
        # Limit results
        if limit:
            query = query.limit(limit)
            
        response = query.execute()
        
        if response.data:
            logger.info(f"Retrieved {len(response.data)} raw options data records")
            return response.data
        else:
            logger.warning("No raw options data found with the provided filters")
            return []
    except Exception as e:
        logger.error(f"Error fetching raw options data: {e}")
        return []

def get_stored_expiry_dates():
    """Get previously stored expiry dates for the current asset"""
    try:
        response = supabase.table('expiry_dates')\
            .select('expiry_date')\
            .eq('asset_name', DEFAULT_ASSET)\
            .execute()
        
        if response.data:
            return [record['expiry_date'] for record in response.data]
        return []
    except Exception as e:
        logger.error(f"Error fetching stored expiry dates: {e}")
        return []

def fetch_options_chain_with_retry(asset, expiry_date, max_retries=3, retry_delay=5):
    """
    Fetch options chain data with retry logic for handling API errors
    
    Args:
        asset (dict): Asset information (security_id, exchange_segment)
        expiry_date (str): Expiry date to fetch data for
        max_retries (int): Maximum number of retry attempts
        retry_delay (int): Delay between retries in seconds
        
    Returns:
        dict: Options chain data or error response
    """
    for attempt in range(max_retries):
        try:
            logger.info(f"Fetching options chain for {DEFAULT_ASSET} expiry: {expiry_date} (Attempt {attempt+1}/{max_retries})")
            options_chain = dhan.option_chain(
                under_security_id=asset['security_id'],
                under_exchange_segment=asset['exchange_segment'],
                expiry=expiry_date
            )
            
            # Check if we got a valid response
            if isinstance(options_chain, dict) and options_chain.get('status') == 'success':
                logger.info(f"Successfully fetched options chain for {expiry_date}")
                return options_chain
            
            # If we got a failure response with empty data or JSON parse error
            if isinstance(options_chain, dict) and options_chain.get('status') == 'failure':
                logger.warning(f"API returned failure for {expiry_date}: {options_chain.get('remarks', 'No error details')}")
                
                # If it's a JSON parsing error, wait and retry
                if 'Expecting value' in str(options_chain.get('remarks', '')):
                    logger.info(f"JSON parsing error detected. Retrying in {retry_delay} seconds...")
                    time.sleep(retry_delay)
                    continue
            
            logger.warning(f"Unexpected response format: {options_chain}")
            return options_chain
            
        except Exception as e:
            logger.error(f"Error fetching options chain for {expiry_date} (Attempt {attempt+1}/{max_retries}): {e}")
            if attempt < max_retries - 1:
                logger.info(f"Retrying in {retry_delay} seconds...")
                time.sleep(retry_delay)
            else:
                logger.error(f"Maximum retry attempts reached for {expiry_date}")
                return {"status": "failure", "remarks": f"Maximum retry attempts reached: {str(e)}", "data": ""}

    return {"status": "failure", "remarks": "All retry attempts failed", "data": ""}

def fetch_and_store_options_data():
    """Main function to fetch and store options data"""
    logger.info(f"\n\n--- Running data collection: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} ---")
    
    try:
        # Fetch expiry dates
        expiry_dates = get_expiry_dates()
        if not expiry_dates:
            logger.error("Could not fetch expiry dates")
            return

        # Get previously stored expiry dates
        stored_dates = get_stored_expiry_dates()
        
        # Check for new dates
        if set(expiry_dates) != set(stored_dates):
            logger.info("Found changes in expiry dates. Updating database...")
            store_expiry_dates(expiry_dates)
        else:
            logger.info("No changes in expiry dates")
        
        logger.info("\nAvailable Expiry Dates:")
        for date in expiry_dates:
            logger.info(date)
        
        # Create directories to store data (for backup)
        os.makedirs('option_data', exist_ok=True)
        os.makedirs('raw_data', exist_ok=True)

        # Fetch options chain for each expiry date
        asset = ASSETS[DEFAULT_ASSET]
        for expiry_date in expiry_dates:  # Process all expiry dates
            try:
                # Use the retry function instead of direct API call
                options_chain = fetch_options_chain_with_retry(asset, expiry_date)
                
                logger.debug(f"Options Chain Response: {json.dumps(options_chain, indent=2)}")
                
                # Save raw response to file (for backup)
                raw_file = f"raw_data/nifty_options_raw_{expiry_date.replace('-', '')}.json"
                with open(raw_file, 'w') as f:
                    json.dump(options_chain, f, indent=2)
                
                # Only process if we got a success response
                if options_chain.get('status') == 'success':
                    process_options_data(options_chain, expiry_date)
                else:
                    logger.warning(f"Skipping processing for {expiry_date} due to API error")
                    
            except Exception as e:
                logger.error(f"Error fetching options chain for {expiry_date}: {e}")
            
            # Add a small delay to avoid rate limiting
            time.sleep(RATE_LIMIT_CONFIG['initial_backoff_seconds'])
        
        logger.info("\nAll options chain data has been processed")
    except Exception as e:
        logger.error(f"Error in data collection process: {e}")
    finally:
        logger.info(f"--- Completed data collection: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} ---\n")

def setup_schedule():
    """Setup scheduled execution of data fetching"""
    logger.info(f"Setting up scheduled execution every {TIME_CONFIG['refresh_interval_seconds']} seconds")
    
    # Create a rate limiter to prevent too many API calls
    last_run_time = [0]  # Using list to make it mutable in nested function
    consecutive_errors = [0]  # Track consecutive errors
    
    def rate_limited_fetch():
        current_time = time.time()
        # Ensure minimum time between API calls
        # if current_time - last_run_time[0] < TIME_CONFIG['rate_limit_seconds']:
        #     logger.debug("Rate limit: Skipping this run to prevent too frequent API calls")
        #     return
            
        try:
            fetch_and_store_options_data()
            last_run_time[0] = current_time
            consecutive_errors[0] = 0  # Reset error count on success
        except Exception as e:
            consecutive_errors[0] += 1
            logger.error(f"Error in scheduled execution: {e}")
            
            # If too many consecutive errors, pause execution
            if consecutive_errors[0] >= ERROR_CONFIG['max_consecutive_errors']:
                logger.error(f"Too many consecutive errors ({consecutive_errors[0]}). Pausing for {ERROR_CONFIG['error_cooldown_seconds']} seconds.")
                time.sleep(ERROR_CONFIG['error_cooldown_seconds'])
                consecutive_errors[0] = 0  # Reset after cooldown
    
    # Run once immediately
    rate_limited_fetch()
    
    # Schedule to run at the specified interval
    schedule.every(TIME_CONFIG['refresh_interval_seconds']).seconds.do(rate_limited_fetch)
    logger.info(f"Scheduled to run every {TIME_CONFIG['refresh_interval_seconds']} seconds")
    
    # Setup signal handling for graceful shutdown
    def signal_handler(sig, frame):
        logger.info("\nReceived shutdown signal. Exiting gracefully...")
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # Keep running indefinitely
    logger.info("Scheduler is running. Press Ctrl+C to exit.")
    try:
        while True:
            schedule.run_pending()
            time.sleep(0.1)  # Small sleep to prevent CPU overuse
    except Exception as e:
        logger.error(f"Error in scheduler: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    setup_schedule()
