# https://dhanhq.co/docs/v2 llms.txt

- [DhanHQ API Overview](https://dhanhq.co/docs/v2/): DhanHQ API for trading and investment services integration.
- [Order Management API](https://dhanhq.co/docs/v2/orders/): Manage orders with APIs for placing, modifying, and retrieving.
- [Portfolio Management API](https://dhanhq.co/docs/v2/portfolio/): API for managing portfolio holdings and positions effectively.
- [CDSL eDIS Stock Selling](https://dhanhq.co/docs/v2/edis/): Complete CDSL eDIS flow for stock selling process.
- [Trading Account Funds](https://dhanhq.co/docs/v2/funds/): Get fund details and margin requirements for trading accounts.
- [Historical Data API](https://dhanhq.co/docs/v2/historical-data/): Access historical candle data for various instruments.
- [Trading API Documentation](https://dhanhq.co/docs/v2/annexure/): Comprehensive guide on trading segments, products, and errors.
- [Forever Order Management](https://dhanhq.co/docs/v2/forever/): Manage Forever Orders with APIs for trading efficiently.
- [Trader's Control API](https://dhanhq.co/docs/v2/traders-control/): Manage trading with the API's kill switch feature.
- [Trade and Ledger APIs](https://dhanhq.co/docs/v2/statements/): APIs for retrieving trade and ledger details effectively.


# HTTP 


Option Chain 
Retrieve real-time Option Chain across exchanges for all underlying. You can fetch Open Interest (OI), Greeks, Volume, Last Traded Price, Best Bid/Ask and Implied Volatility (IV) across all strikes for any underlying.

    curl --location 'https://api.dhan.co/v2/optionchain' \
    --header 'access-token: JWT' \
    --header 'client-id: ClientId' \
    --header 'Content-Type: application/json' \
    --data '{}'


Header	Description
access-token //	Access Token generated via Dhan
client-id //User specific identification generated by Dhan

Request Structure
    {
    "UnderlyingScrip":13,
    "UnderlyingSeg":"IDX_I",
    "Expiry":"2024-10-31"
    }

Response Structure

{
    "data": {
        "last_price": 24964.25,
        "oc": {
            .
            .
            "25000.000000": {
                "ce": {
                    "greeks": {
                        "delta": 0.52546,
                        "theta": -12.88756,
                        "gamma": 0.00136,
                        "vega": 12.98931
                    },
                    "implied_volatility": 8.945204889199001,
                    "last_price": 125.05,
                    "oi": 5962675,
                    "previous_close_price": 190.45,
                    "previous_oi": 3939375,
                    "previous_volume": 831463,
                    "top_ask_price": 124.9,
                    "top_ask_quantity": 1000,
                    "top_bid_price": 124,
                    "top_bid_quantity": 100,
                    "volume": 84202625
                },
                "pe": {
                    "greeks": {
                        "delta": -0.48099,
                        "theta": -10.56587,
                        "gamma": 0.00092,
                        "vega": 13.00105
                    },
                    "implied_volatility": 13.321804909313869,
                    "last_price": 165,
                    "oi": 5059700,
                    "previous_close_price": 153.6,
                    "previous_oi": 4667700,
                    "previous_volume": 1047989,
                    "top_ask_price": 165,
                    "top_ask_quantity": 375,
                    "top_bid_price": 164.05,
                    "top_bid_quantity": 50,
                    "volume": 81097175
                }
            }
            .
            .
            .
        }
    }
}


# Expiry List

Retrieve dates of all expiries of any underlying, for which Options Instruments are active.

    curl --request POST \
    --url https://api.dhan.co/v2/optionchain/expirylist \
    --header 'Content-Type: application/json' \
    --header 'access-token: JWT' \
    --header 'client-id: 1000000001' \
    --data '{}'

Header	Description
access-token //	Access Token generated via Dhan
client-id //User specific identification generated by Dhan

Request Structure
    {
    "UnderlyingScrip":13,
    "UnderlyingSeg":"IDX_I"
    }

Response Structure
    {
    "data": [
        "2024-10-17",
        "2024-10-24",
        "2024-10-31",
        "2024-11-07",
        "2024-11-14",
        "2024-11-28",
        "2024-12-26",
        "2025-03-27",
        "2025-06-26",
        "2025-09-25",
        "2025-12-24",
        "2026-06-25",
        "2026-12-31",
        "2027-06-24",
        "2027-12-30",
        "2028-06-29",
        "2028-12-28",
        "2029-06-28"
    ],
    "status": "success"
    }




#DhanHQ API for Order Management
https://pypi.org/project/dhanhq/

from dhanhq import dhanhq

dhan = dhanhq("client_id","access_token")

# Place an order for Equity Cash
dhan.place_order(security_id='1333',            # HDFC Bank
    exchange_segment=dhan.NSE,
    transaction_type=dhan.BUY,
    quantity=10,
    order_type=dhan.MARKET,
    product_type=dhan.INTRA,
    price=0)
    
# Place an order for NSE Futures & Options
dhan.place_order(security_id='52175',           # Nifty PE
    exchange_segment=dhan.NSE_FNO,
    transaction_type=dhan.BUY,
    quantity=550,
    order_type=dhan.MARKET,
    product_type=dhan.INTRA,
    price=0)
  
# Fetch all orders
dhan.get_order_list()

# Get order by id
dhan.get_order_by_id(order_id)

# Modify order
dhan.modify_order(order_id, order_type, leg_name, quantity, price, trigger_price, disclosed_quantity, validity)

# Cancel order
dhan.cancel_order(order_id)

# Get order by correlation id
dhan.get_order_by_corelationID(corelationID)

# Get Instrument List
dhan.fetch_security_list("compact")

# Get positions
dhan.get_positions()

# Get holdings
dhan.get_holdings()

# Intraday Minute Data
dhan.intraday_minute_data(security_id,exchange_segment,instrument_type)

# Historical Daily Data
dhan.historical_daily_data(security_id,exchange_segment,instrument_type,expiry_code,from_date,to_date)

# Time Converter
dhan.convert_to_date_time(EPOCH Date)

# Get trade book
dhan.get_trade_book(order_id)

# Get trade history
dhan.get_trade_history(from_date,to_date,page_number=0)

# Get fund limits
dhan.get_fund_limits()

# Generate TPIN
dhan.generate_tpin()

# Enter TPIN in Form
dhan.open_browser_for_tpin(isin='INE00IN01015',
    qty=1,
    exchange='NSE')

# EDIS Status and Inquiry
dhan.edis_inquiry()

# Expiry List of Underlying
dhan.expiry_list(
    under_security_id=13,                       # Nifty
    under_exchange_segment="IDX_I"
)

# Option Chain
dhan.option_chain(
    under_security_id=13,                       # Nifty
    under_exchange_segment="IDX_I",
    expiry="2024-10-31"
)

# Market Quote Data                     # LTP - ticker_data, OHLC - ohlc_data, Full Packet - quote_data
dhan.ohlc_data(
    securities = {"NSE_EQ":[1333]}
)

# Place Forever Order (SINGLE)
dhan.place_forever(
    security_id="1333",
    exchange_segment= dhan.NSE,
    transaction_type= dhan.BUY,
    product_type=dhan.CNC,
    product_type= dhan.LIMIT,
    quantity= 10,
    price= 1900,
    trigger_Price= 1950
)

#Market Feed Usage

from dhanhq import marketfeed

# Add your Dhan Client ID and Access Token
client_id = "Dhan Client ID"
access_token = "Access Token"

# Structure for subscribing is (exchange_segment, "security_id", subscription_type)

instruments = [(marketfeed.NSE, "1333", marketfeed.Ticker),   # Ticker - Ticker Data
    (marketfeed.NSE, "1333", marketfeed.Quote),     # Quote - Quote Data
    (marketfeed.NSE, "1333", marketfeed.Full),      # Full - Full Packet
    (marketfeed.NSE, "11915", marketfeed.Ticker),
    (marketfeed.NSE, "11915", marketfeed.Full)]

version = "v2"          # Mention Version and set to latest version 'v2'

# In case subscription_type is left as blank, by default Ticker mode will be subscribed.

try:
    data = marketfeed.DhanFeed(client_id, access_token, instruments, version)
    while True:
        data.run_forever()
        response = data.get_data()
        print(response)

except Exception as e:
    print(e)

# Close Connection
data.disconnect()

# Subscribe instruments while connection is open
sub_instruments = [(marketfeed.NSE, "14436", marketfeed.Ticker)]

data.subscribe_symbols(sub_instruments)

# Unsubscribe instruments which are already active on connection
unsub_instruments = [(marketfeed.NSE, "1333", 16)]

data.unsubscribe_symbols(unsub_instruments)


Live Order Update Usage
from dhanhq import orderupdate
import time

# Add your Dhan Client ID and Access Token
client_id = "Dhan Client ID"
access_token = "Access Token"

def run_order_update():
    order_client = orderupdate.OrderSocket(client_id, access_token)
    while True:
        try:
            order_client.connect_to_dhan_websocket_sync()
        except Exception as e:
            print(f"Error connecting to Dhan WebSocket: {e}. Reconnecting in 5 seconds...")
            time.sleep(5)

run_order_update()