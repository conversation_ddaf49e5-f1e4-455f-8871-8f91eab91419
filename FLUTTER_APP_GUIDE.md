# Flutter App Guide for Options Data

## Model Classes

Below are the model classes you'll need to properly handle the options data from Supabase. These classes include null safety and proper type checking.

```dart
// lib/models/option_data.dart

import 'package:freezed_annotation/freezed_annotation.dart';

part 'option_data.freezed.dart';
part 'option_data.g.dart';

@freezed
class OptionData with _$OptionData {
  const factory OptionData({
    required String timestamp,
    required String expiry_date,
    required double strike_price,
    required String option_type,
    required double spot_price,
    required double last_price,
    required double previous_close_price,
    required int volume,
    required int previous_volume,
    required int oi,
    required int previous_oi,
    required double top_bid_price,
    required double top_ask_price,
    required int top_bid_quantity,
    required int top_ask_quantity,
    required double iv,
    required double delta,
    required double gamma,
    required double theta,
    required double vega,
  }) = _OptionData;

  factory OptionData.fromJson(Map<String, dynamic> json) => _$OptionDataFromJson(json);

  // Custom fromMap method with null safety
  factory OptionData.fromMap(Map<String, dynamic> map) {
    return OptionData(
      timestamp: map['timestamp'] as String? ?? '',
      expiry_date: map['expiry_date'] as String? ?? '',
      strike_price: (map['strike_price'] as num?)?.toDouble() ?? 0.0,
      option_type: map['option_type'] as String? ?? '',
      spot_price: (map['spot_price'] as num?)?.toDouble() ?? 0.0,
      last_price: (map['last_price'] as num?)?.toDouble() ?? 0.0,
      previous_close_price: (map['previous_close_price'] as num?)?.toDouble() ?? 0.0,
      volume: (map['volume'] as num?)?.toInt() ?? 0,
      previous_volume: (map['previous_volume'] as num?)?.toInt() ?? 0,
      oi: (map['oi'] as num?)?.toInt() ?? 0,
      previous_oi: (map['previous_oi'] as num?)?.toInt() ?? 0,
      top_bid_price: (map['top_bid_price'] as num?)?.toDouble() ?? 0.0,
      top_ask_price: (map['top_ask_price'] as num?)?.toDouble() ?? 0.0,
      top_bid_quantity: (map['top_bid_quantity'] as num?)?.toInt() ?? 0,
      top_ask_quantity: (map['top_ask_quantity'] as num?)?.toInt() ?? 0,
      iv: (map['iv'] as num?)?.toDouble() ?? 0.0,
      delta: (map['delta'] as num?)?.toDouble() ?? 0.0,
      gamma: (map['gamma'] as num?)?.toDouble() ?? 0.0,
      theta: (map['theta'] as num?)?.toDouble() ?? 0.0,
      vega: (map['vega'] as num?)?.toDouble() ?? 0.0,
    );
  }
}
```

## Setup Instructions

1. Add the following dependencies to your `pubspec.yaml`:

```yaml
dependencies:
  freezed_annotation: ^2.4.1
  json_annotation: ^4.8.1

dev_dependencies:
  build_runner: ^2.4.6
  freezed: ^2.4.5
  json_serializable: ^6.7.1
```

2. Run the following commands to generate the model files:

```bash
flutter pub get
flutter pub run build_runner build --delete-conflicting-outputs
```

## Usage Example

Here's how to use the model with Supabase:

```dart
// lib/services/options_service.dart

import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/option_data.dart';

class OptionsService {
  final SupabaseClient _supabase;

  OptionsService(this._supabase);

  Stream<List<OptionData>> streamOptionsData() {
    return _supabase
        .from('nifty')
        .stream(primaryKey: ['timestamp', 'strike_price', 'option_type'])
        .map((List<Map<String, dynamic>> data) {
          return data.map((map) => OptionData.fromMap(map)).toList();
        });
  }

  Future<List<OptionData>> getOptionsData() async {
    final response = await _supabase
        .from('nifty')
        .select()
        .order('timestamp', ascending: false)
        .limit(100);

    return (response as List)
        .map((map) => OptionData.fromMap(map as Map<String, dynamic>))
        .toList();
  }
}
```

## Usage in Widget

Here's how to use the service in your Flutter widget:

```dart
// lib/widgets/options_list.dart

import 'package:flutter/material.dart';
import '../models/option_data.dart';
import '../services/options_service.dart';

class OptionsListWidget extends StatelessWidget {
  final OptionsService optionsService;

  const OptionsListWidget({Key? key, required this.optionsService}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<List<OptionData>>(
      stream: optionsService.streamOptionsData(),
      builder: (context, snapshot) {
        if (snapshot.hasError) {
          return Text('Error: ${snapshot.error}');
        }

        if (!snapshot.hasData) {
          return const CircularProgressIndicator();
        }

        final options = snapshot.data!;
        return ListView.builder(
          itemCount: options.length,
          itemBuilder: (context, index) {
            final option = options[index];
            return ListTile(
              title: Text('${option.option_type} - Strike: ${option.strike_price}'),
              subtitle: Text('Last Price: ${option.last_price}'),
            );
          },
        );
      },
    );
  }
}
```

## Error Handling

To handle potential errors in your data stream, you can add a try-catch block in your service:

```dart
// lib/services/options_service.dart

Stream<List<OptionData>> streamOptionsData() {
  return _supabase
      .from('nifty')
      .stream(primaryKey: ['timestamp', 'strike_price', 'option_type'])
      .map((List<Map<String, dynamic>> data) {
        try {
          return data.map((map) => OptionData.fromMap(map)).toList();
        } catch (e, stackTrace) {
          print('Error parsing options data: $e\n$stackTrace');
          // You can either return an empty list or rethrow the error
          // depending on your error handling strategy
          return [];
          // or
          // throw Exception('Failed to parse options data: $e');
        }
      });
}
```

## Common Issues and Solutions

1. **Null Values**: The model's `fromMap` method includes null checks and default values for all fields to prevent null errors.

2. **Type Mismatches**: The model handles type conversion for numbers (int/double) to prevent type casting errors.

3. **Missing Fields**: Default values are provided for all fields in case they're missing from the response.

4. **Stream Errors**: The StreamBuilder widget properly handles error states and loading states.

## Best Practices

1. Always use the provided `fromMap` method instead of direct JSON parsing.
2. Implement proper error handling in your services.
3. Use StreamBuilder's error and loading states appropriately.
4. Consider implementing retry logic for failed requests.
5. Log errors appropriately for debugging purposes.

## Testing

Here's an example of how to test your model:

```dart
// test/models/option_data_test.dart

import 'package:flutter_test/flutter_test.dart';
import 'package:your_app/models/option_data.dart';

void main() {
  test('OptionData.fromMap handles null values', () {
    final map = <String, dynamic>{};
    final option = OptionData.fromMap(map);
    
    expect(option.timestamp, '');
    expect(option.strike_price, 0.0);
    expect(option.option_type, '');
    // ... test other fields
  });

  test('OptionData.fromMap handles valid data', () {
    final map = {
      'timestamp': '2024-03-14T10:00:00',
      'strike_price': 18000.0,
      'option_type': 'CE',
      // ... add other fields
    };
    
    final option = OptionData.fromMap(map);
    
    expect(option.timestamp, '2024-03-14T10:00:00');
    expect(option.strike_price, 18000.0);
    expect(option.option_type, 'CE');
    // ... test other fields
  });
}