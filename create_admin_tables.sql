-- Create admin tables for managing option chain data collection

-- Table for managing assets
CREATE TABLE IF NOT EXISTS public.option_assets (
    id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    asset_code VARCHAR(20) UNIQUE NOT NULL,
    security_id INTEGER NOT NULL,
    exchange_segment VARCHAR(10) NOT NULL,
    lot_size INTEGER NOT NULL,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    priority INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Table for tracking scheduled tasks and their status
CREATE TABLE IF NOT EXISTS public.scheduled_tasks (
    id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    task_name VARCHAR(100) NOT NULL,
    asset_code VARCHAR(20) REFERENCES public.option_assets(asset_code),
    last_run_at TIMESTAMP WITH TIME ZONE,
    next_run_at TIMESTAMP WITH TIME ZONE,
    status VARCHAR(20) DEFAULT 'pending',
    interval_seconds INTEGER NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    error_count INTEGER DEFAULT 0,
    last_error TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Table for storing configuration settings
CREATE TABLE IF NOT EXISTS public.app_settings (
    id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value JSONB NOT NULL,
    description TEXT,
    is_admin_only BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Table for audit logging
CREATE TABLE IF NOT EXISTS public.admin_audit_log (
    id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id),
    action VARCHAR(50) NOT NULL,
    table_name VARCHAR(100),
    record_id VARCHAR(100),
    old_values JSONB,
    new_values JSONB,
    ip_address VARCHAR(50),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_timestamp()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply update_timestamp trigger to all tables with updated_at
CREATE TRIGGER update_option_assets_timestamp
BEFORE UPDATE ON public.option_assets
FOR EACH ROW EXECUTE FUNCTION update_timestamp();

CREATE TRIGGER update_scheduled_tasks_timestamp
BEFORE UPDATE ON public.scheduled_tasks
FOR EACH ROW EXECUTE FUNCTION update_timestamp();

CREATE TRIGGER update_app_settings_timestamp
BEFORE UPDATE ON public.app_settings
FOR EACH ROW EXECUTE FUNCTION update_timestamp();

-- Create function to log admin actions
CREATE OR REPLACE FUNCTION log_admin_action()
RETURNS TRIGGER AS $$
DECLARE
    old_data JSONB := null;
    new_data JSONB := null;
BEGIN
    IF (TG_OP = 'UPDATE') THEN
        old_data = to_jsonb(OLD);
        new_data = to_jsonb(NEW);
    ELSIF (TG_OP = 'INSERT') THEN
        new_data = to_jsonb(NEW);
    ELSIF (TG_OP = 'DELETE') THEN
        old_data = to_jsonb(OLD);
    END IF;

    INSERT INTO public.admin_audit_log (
        user_id,
        action,
        table_name,
        record_id,
        old_values,
        new_values,
        ip_address
    ) VALUES (
        auth.uid(),
        TG_OP,
        TG_TABLE_NAME,
        CASE
            WHEN TG_OP = 'DELETE' THEN OLD.id::text
            ELSE NEW.id::text
        END,
        old_data,
        new_data,
        COALESCE(
            (SELECT session_data->>'client_ip' 
             FROM auth.sessions 
             WHERE user_id = auth.uid() 
             ORDER BY created_at DESC 
             LIMIT 1),
            'unknown'
        )
    );

    RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Apply audit logging trigger to all admin tables
CREATE TRIGGER log_option_assets_changes
AFTER INSERT OR UPDATE OR DELETE ON public.option_assets
FOR EACH ROW EXECUTE FUNCTION log_admin_action();

CREATE TRIGGER log_scheduled_tasks_changes
AFTER INSERT OR UPDATE OR DELETE ON public.scheduled_tasks
FOR EACH ROW EXECUTE FUNCTION log_admin_action();

CREATE TRIGGER log_app_settings_changes
AFTER INSERT OR UPDATE OR DELETE ON public.app_settings
FOR EACH ROW EXECUTE FUNCTION log_admin_action();

-- Function to initialize settings from the config file
CREATE OR REPLACE FUNCTION initialize_app_settings()
RETURNS void AS $$
BEGIN
    -- Insert default settings if they don't exist
    INSERT INTO public.app_settings (setting_key, setting_value, description, is_admin_only)
    VALUES
        ('option_chain_settings', '{
            "data_refresh_interval_seconds": 5,
            "expiry_refresh_interval_minutes": 30,
            "max_expiries_to_fetch": 5,
            "fetch_monthly_expiries": true,
            "fetch_weekly_expiries": true,
            "collect_greeks": true,
            "store_raw_data": true
        }'::jsonb, 'Settings for option chain data collection', true),
        
        ('admin_settings', '{
            "enable_admin_ui": true,
            "admin_access_roles": ["admin"],
            "allow_asset_management": true,
            "allow_schedule_changes": true,
            "allow_database_operations": true,
            "max_concurrent_assets": 5,
            "require_auth_for_data_access": false
        }'::jsonb, 'Admin control panel settings', true),
        
        ('database_config', '{
            "options_table_prefix": "options_",
            "raw_data_table_prefix": "raw_",
            "batch_size": 1000,
            "cleanup_days": 30,
            "backup_enabled": true,
            "compress_old_data": true
        }'::jsonb, 'Database configuration settings', true)
    ON CONFLICT (setting_key) DO NOTHING;
END;
$$ LANGUAGE plpgsql;

-- Add Row Level Security policies
ALTER TABLE public.option_assets ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.scheduled_tasks ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.app_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.admin_audit_log ENABLE ROW LEVEL SECURITY;

-- Policy for viewing assets - everyone can view
CREATE POLICY option_assets_select_policy ON public.option_assets
    FOR SELECT USING (true);

-- Policy for modifying assets - only admins can modify
CREATE POLICY option_assets_modify_policy ON public.option_assets
    FOR ALL USING (auth.jwt() ? 'role' AND auth.jwt()->>'role' = 'admin');

-- Policy for viewing tasks - everyone can view
CREATE POLICY scheduled_tasks_select_policy ON public.scheduled_tasks
    FOR SELECT USING (true);

-- Policy for modifying tasks - only admins can modify
CREATE POLICY scheduled_tasks_modify_policy ON public.scheduled_tasks
    FOR ALL USING (auth.jwt() ? 'role' AND auth.jwt()->>'role' = 'admin');

-- Policy for viewing settings - public settings visible to all, admin settings only to admins
CREATE POLICY app_settings_select_policy ON public.app_settings
    FOR SELECT USING (
        NOT is_admin_only OR 
        (auth.jwt() ? 'role' AND auth.jwt()->>'role' = 'admin')
    );

-- Policy for modifying settings - only admins can modify
CREATE POLICY app_settings_modify_policy ON public.app_settings
    FOR ALL USING (auth.jwt() ? 'role' AND auth.jwt()->>'role' = 'admin');

-- Policy for viewing audit logs - only admins can view
CREATE POLICY admin_audit_log_select_policy ON public.admin_audit_log
    FOR SELECT USING (auth.jwt() ? 'role' AND auth.jwt()->>'role' = 'admin');

-- Initialize default settings
SELECT initialize_app_settings(); 