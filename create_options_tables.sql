-- Create tables for storing option chain data for multiple assets

-- Create or replace function to generate option data tables for an asset
CREATE OR REPLACE FUNCTION create_option_tables(asset_code VARCHAR)
RETURNS VOID AS $$
DECLARE
    options_table_name TEXT;
    raw_table_name TEXT;
    unique_constraint TEXT;
BEGIN
    -- Generate table names
    options_table_name := 'options_' || lower(asset_code);
    raw_table_name := 'raw_' || lower(asset_code);
    unique_constraint := lower(asset_code) || '_unique_record';
    
    -- Create the main options data table
    EXECUTE format('
        CREATE TABLE IF NOT EXISTS public.%I (
            id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
            timestamp TIMESTAMP WITH TIME ZONE,
            expiry_date TEXT,
            strike_price NUMERIC(10, 2),
            option_type TEXT CHECK (option_type IN (''CE'', ''PE'')),
            spot_price NUMERIC(10, 2),
            
            -- Price related fields
            last_price NUMERIC(10, 2),
            previous_close_price NUMERIC(10, 2),
            top_bid_price NUMERIC(10, 2),
            top_ask_price NUMERIC(10, 2),
            
            -- Volume and OI fields
            volume INTEGER,
            previous_volume INTEGER,
            oi INTEGER,
            previous_oi INTEGER,
            top_bid_quantity INTEGER,
            top_ask_quantity INTEGER,
            
            -- Greeks and IV
            iv NUMERIC(10, 4),
            delta NUMERIC(10, 4),
            gamma NUMERIC(10, 6),
            theta NUMERIC(10, 4),
            vega NUMERIC(10, 4),
            
            -- Metadata
            asset_code VARCHAR(20) NOT NULL,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone(''utc''::text, now()),
            
            -- Add unique constraint for the combination
            CONSTRAINT %I UNIQUE (timestamp, expiry_date, strike_price, option_type)
        )', options_table_name, unique_constraint);

    -- Create indexes for better query performance
    EXECUTE format('
        CREATE INDEX IF NOT EXISTS idx_%I_expiry_date ON public.%I(expiry_date);
        CREATE INDEX IF NOT EXISTS idx_%I_timestamp ON public.%I(timestamp);
        CREATE INDEX IF NOT EXISTS idx_%I_strike_price ON public.%I(strike_price);
        CREATE INDEX IF NOT EXISTS idx_%I_option_type ON public.%I(option_type);
        CREATE INDEX IF NOT EXISTS idx_%I_asset_code ON public.%I(asset_code);
        CREATE INDEX IF NOT EXISTS idx_%I_composite ON public.%I(expiry_date, strike_price, option_type);
    ', 
        lower(asset_code), options_table_name,
        lower(asset_code), options_table_name,
        lower(asset_code), options_table_name,
        lower(asset_code), options_table_name,
        lower(asset_code), options_table_name,
        lower(asset_code), options_table_name
    );

    -- Create raw data table for storing complete API responses
    EXECUTE format('
        CREATE TABLE IF NOT EXISTS public.%I (
            id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
            expiry_date TEXT,
            asset_code VARCHAR(20) NOT NULL,
            raw_data JSONB NOT NULL,
            spot_price NUMERIC(10, 2),
            created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone(''utc''::text, now())
        )', raw_table_name);

    -- Create indexes for raw data table
    EXECUTE format('
        CREATE INDEX IF NOT EXISTS idx_%I_expiry_date ON public.%I(expiry_date);
        CREATE INDEX IF NOT EXISTS idx_%I_created_at ON public.%I(created_at);
        CREATE INDEX IF NOT EXISTS idx_%I_asset_code ON public.%I(asset_code);
    ', 
        raw_table_name, raw_table_name,
        raw_table_name, raw_table_name,
        raw_table_name, raw_table_name
    );

    -- Enable Row Level Security (RLS)
    EXECUTE format('ALTER TABLE public.%I ENABLE ROW LEVEL SECURITY', options_table_name);
    EXECUTE format('ALTER TABLE public.%I ENABLE ROW LEVEL SECURITY', raw_table_name);

    -- Create policies for access control
    EXECUTE format('
        DO $$ 
        BEGIN
            DROP POLICY IF EXISTS "Enable read access for all users" ON public.%I;
            DROP POLICY IF EXISTS "Enable insert access for authenticated users" ON public.%I;
            DROP POLICY IF EXISTS "Enable read access for all users" ON public.%I;
            DROP POLICY IF EXISTS "Enable insert access for authenticated users" ON public.%I;
        EXCEPTION WHEN undefined_object THEN 
            NULL;
        END $$;

        CREATE POLICY "Enable read access for all users" ON public.%I
            FOR SELECT USING (true);

        CREATE POLICY "Enable insert access for authenticated users" ON public.%I
            FOR INSERT WITH CHECK (true);

        CREATE POLICY "Enable read access for all users" ON public.%I
            FOR SELECT USING (true);

        CREATE POLICY "Enable insert access for authenticated users" ON public.%I
            FOR INSERT WITH CHECK (true);
    ', 
        options_table_name, options_table_name, 
        raw_table_name, raw_table_name,
        options_table_name, options_table_name,
        raw_table_name, raw_table_name
    );

    -- Create view for the latest option chain data
    EXECUTE format('
        CREATE OR REPLACE VIEW public.%I_view AS
        WITH latest_data AS (
            SELECT DISTINCT ON (strike_price, option_type, expiry_date)
                *
            FROM public.%I
            ORDER BY strike_price, option_type, expiry_date, timestamp DESC
        )
        SELECT 
            expiry_date,
            strike_price,
            spot_price,
            MAX(CASE WHEN option_type = ''CE'' THEN last_price END) as call_price,
            MAX(CASE WHEN option_type = ''PE'' THEN last_price END) as put_price,
            MAX(CASE WHEN option_type = ''CE'' THEN oi END) as call_oi,
            MAX(CASE WHEN option_type = ''PE'' THEN oi END) as put_oi,
            MAX(CASE WHEN option_type = ''CE'' THEN volume END) as call_volume,
            MAX(CASE WHEN option_type = ''PE'' THEN volume END) as put_volume,
            MAX(CASE WHEN option_type = ''CE'' THEN iv END) as call_iv,
            MAX(CASE WHEN option_type = ''PE'' THEN iv END) as put_iv,
            asset_code
        FROM latest_data
        GROUP BY expiry_date, strike_price, spot_price, asset_code
        ORDER BY strike_price;
    ', lower(asset_code), options_table_name);

    -- Create function to clean old data
    EXECUTE format('
        CREATE OR REPLACE FUNCTION public.clean_old_%I_data(days_to_keep INTEGER)
        RETURNS INTEGER
        LANGUAGE plpgsql
        AS $$
        DECLARE
            rows_deleted INTEGER;
        BEGIN
            DELETE FROM public.%I
            WHERE timestamp < (NOW() - (days_to_keep || '' days'')::INTERVAL);
            GET DIAGNOSTICS rows_deleted = ROW_COUNT;
            
            DELETE FROM public.%I
            WHERE created_at < (NOW() - (days_to_keep || '' days'')::INTERVAL);
            
            RETURN rows_deleted;
        END;
        $$;
    ', lower(asset_code), options_table_name, raw_table_name);

    -- Add comments
    EXECUTE format('
        COMMENT ON TABLE public.%I IS ''Stores complete %I options chain data from DhanHQ API'';
        COMMENT ON TABLE public.%I IS ''Stores raw API responses for %I options chain data'';
        COMMENT ON VIEW public.%I_view IS ''Provides paired call/put data for %I option chain visualization'';
        COMMENT ON FUNCTION public.clean_old_%I_data IS ''Cleans up old %I options data older than specified days'';
    ', 
        options_table_name, asset_code,
        raw_table_name, asset_code,
        lower(asset_code), asset_code,
        lower(asset_code), asset_code
    );
    
    RAISE NOTICE 'Successfully created tables for asset %', asset_code;
END;
$$ LANGUAGE plpgsql;

-- Function to initialize tables for all assets in the option_assets table
CREATE OR REPLACE FUNCTION initialize_option_tables()
RETURNS VOID AS $$
DECLARE
    asset_rec RECORD;
BEGIN
    -- Create tables for each active asset
    FOR asset_rec IN SELECT asset_code FROM public.option_assets WHERE is_active = true
    LOOP
        PERFORM create_option_tables(asset_rec.asset_code);
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Function to create tables for the initial set of assets
CREATE OR REPLACE FUNCTION create_initial_option_tables()
RETURNS VOID AS $$
BEGIN
    -- Create tables for the main indices
    PERFORM create_option_tables('NIFTY');
    PERFORM create_option_tables('BANKNIFTY');
    PERFORM create_option_tables('FINNIFTY');
    PERFORM create_option_tables('MIDCPNIFTY');
END;
$$ LANGUAGE plpgsql;

-- Function to create a new table for an asset (for admin use)
CREATE OR REPLACE FUNCTION admin_create_option_tables(asset_code VARCHAR)
RETURNS VOID AS $$
BEGIN
    -- Check if user has admin privileges
    IF (auth.jwt() ? 'role' AND auth.jwt()->>'role' = 'admin') THEN
        PERFORM create_option_tables(asset_code);
    ELSE
        RAISE EXCEPTION 'Admin privileges required to create new option tables';
    END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Execute the initial table creation function
SELECT create_initial_option_tables(); 