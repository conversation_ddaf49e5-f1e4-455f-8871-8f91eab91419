import os
from dotenv import load_dotenv
from supabase import create_client, Client
import pandas as pd
from datetime import datetime, timedelta
import pytz
import logging

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# Initialize Supabase client
supabase: Client = create_client(
    os.getenv('SUPABASE_URL'),
    os.getenv('SUPABASE_KEY')
)

def verify_data_storage():
    """Verify if data is being stored in Supabase"""
    try:
        # 1. Check if table exists and get total count
        response = supabase.table('nifty').select('count').execute()
        total_count = response.data[0]['count'] if response.data else 0
        logger.info(f"Total records in nifty table: {total_count}")

        if total_count == 0:
            logger.warning("No data found in the nifty table")
            return False

        # 2. Get unique expiry dates
        response = supabase.table('nifty').select('expiry_date').execute()
        expiry_dates = list(set([record['expiry_date'] for record in response.data])) if response.data else []
        logger.info(f"Available expiry dates: {expiry_dates}")

        # 3. Get latest data timestamp
        response = supabase.table('nifty').select('timestamp').order('timestamp', desc=True).limit(1).execute()
        latest_timestamp = response.data[0]['timestamp'] if response.data else None
        logger.info(f"Latest data timestamp: {latest_timestamp}")

        # 4. Check data for each expiry date
        for expiry in expiry_dates:
            response = supabase.table('nifty')\
                .select('strike_price', 'option_type', 'last_price', 'oi', 'iv')\
                .eq('expiry_date', expiry)\
                .execute()
            
            if response.data:
                df = pd.DataFrame(response.data)
                summary = {
                    'total_strikes': len(df['strike_price'].unique()),
                    'ce_count': len(df[df['option_type'] == 'CE']),
                    'pe_count': len(df[df['option_type'] == 'PE']),
                    'avg_iv': df['iv'].mean(),
                    'total_oi': df['oi'].sum()
                }
                logger.info(f"\nSummary for expiry {expiry}:")
                for key, value in summary.items():
                    logger.info(f"{key}: {value}")
            else:
                logger.warning(f"No data found for expiry {expiry}")

        # 5. Check recent data insertion
        ist = pytz.timezone('Asia/Kolkata')
        now = datetime.now(ist)
        one_hour_ago = now - timedelta(hours=1)
        
        response = supabase.table('nifty')\
            .select('count')\
            .gte('timestamp', one_hour_ago.isoformat())\
            .execute()
        
        recent_count = response.data[0]['count'] if response.data else 0
        logger.info(f"\nRecords inserted in the last hour: {recent_count}")

        return True

    except Exception as e:
        logger.error(f"Error verifying data storage: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def main():
    logger.info("Starting data storage verification...")
    success = verify_data_storage()
    if success:
        logger.info("Data storage verification completed successfully")
    else:
        logger.error("Data storage verification failed")

if __name__ == "__main__":
    main() 