"""
Setup Database Schema

This script sets up the necessary database schema for the options chain data management system.
It creates admin tables, option chain tables, and initializes the database with configuration settings.
"""

import os
import sys
import logging
import subprocess
from dotenv import load_dotenv
from supabase import create_client, Client

# Import admin API for database initialization
from admin_api import AdminAPI

# Load environment variables
load_dotenv()

# Initialize logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Supabase credentials
SUPABASE_URL = os.getenv('SUPABASE_URL')
SUPABASE_KEY = os.getenv('SUPABASE_KEY')

# Validate credentials
if not SUPABASE_URL or not SUPABASE_KEY:
    raise ValueError("Please set SUPABASE_URL and SUPABASE_KEY in your .env file")

# Initialize Supabase client
supabase: Client = create_client(
    SUPABASE_URL,
    SUPABASE_KEY
)

def run_sql_script(script_file):
    """
    Run a SQL script using psql
    
    Args:
        script_file: Path to SQL script file
        
    Returns:
        Tuple of (success, output)
    """
    db_url = os.getenv('DATABASE_URL')
    if not db_url:
        logger.error("DATABASE_URL not set in environment")
        return False, "DATABASE_URL not set"
        
    try:
        # Run the script using psql
        result = subprocess.run(
            ['psql', db_url, '-f', script_file],
            capture_output=True,
            text=True
        )
        
        if result.returncode == 0:
            logger.info(f"Successfully executed SQL script: {script_file}")
            return True, result.stdout
        else:
            logger.error(f"Error executing SQL script {script_file}: {result.stderr}")
            return False, result.stderr
    except Exception as e:
        logger.error(f"Error running SQL script {script_file}: {e}")
        return False, str(e)

def execute_sql(sql):
    """
    Execute SQL directly through Supabase
    
    Args:
        sql: SQL command to execute
        
    Returns:
        Success status
    """
    try:
        response = supabase.rpc('exec_sql', {'query': sql}).execute()
        logger.info(f"SQL executed successfully")
        return True
    except Exception as e:
        logger.error(f"Error executing SQL: {e}")
        return False

def setup_database():
    """
    Set up the database schema
    
    Returns:
        Success status
    """
    logger.info("Setting up database schema...")
    
    # Step 1: Create admin tables
    logger.info("Creating admin tables...")
    success, output = run_sql_script('create_admin_tables.sql')
    if not success:
        logger.error("Failed to create admin tables")
        return False
    
    # Step 2: Initialize from configuration
    logger.info("Initializing database from configuration...")
    result = AdminAPI.initialize_from_config()
    if isinstance(result, dict) and 'error' in result:
        logger.error(f"Failed to initialize database from configuration: {result['error']}")
        return False
    
    # Step 3: Create option chain tables
    logger.info("Creating option chain tables...")
    success, output = run_sql_script('create_options_tables.sql')
    if not success:
        logger.error("Failed to create option chain tables")
        return False
    
    logger.info("Database schema setup completed successfully")
    return True

def main():
    """Main function"""
    try:
        # Setup database schema
        if setup_database():
            logger.info("Database setup completed successfully")
            return 0
        else:
            logger.error("Database setup failed")
            return 1
    except Exception as e:
        logger.error(f"Error setting up database: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return 1

if __name__ == "__main__":
    sys.exit(main())