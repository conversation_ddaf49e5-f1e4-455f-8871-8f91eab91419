import os
from supabase import create_client, Client
from dotenv import load_dotenv

load_dotenv()

# Initialize Supabase client
supabase: Client = create_client(
    os.getenv('SUPABASE_URL'),
    os.getenv('SUPABASE_KEY')
)

try:
    # Create a sample record to ensure table creation with all columns
    sample_record = {
        'timestamp': '2024-03-28T00:00:00+00:00',
        'expiry_date': '2024-03-28',
        'strike_price': 0,
        'option_type': 'CE',
        'spot_price': 0,
        'last_price': 0,
        'volume': 0,
        'oi': 0,
        'iv': 0,
        'delta': 0,
        'gamma': 0,
        'theta': 0,
        'vega': 0
    }
    
    # Insert the sample record
    response = supabase.table('nifty').insert(sample_record).execute()
    print('Table schema updated successfully!')
    
    # Delete the sample record
    supabase.table('nifty').delete().execute()
    print('Sample record cleaned up!')
    
except Exception as e:
    print(f'Error updating schema: {e}') 