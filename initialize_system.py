#!/usr/bin/env python3
"""
Options Chain Data Management System Initializer

This script initializes the options chain data management system by:
1. Setting up the database schema
2. Loading initial configuration
3. Starting the data collection process
"""

import os
import sys
import logging
import argparse
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("system_init.log")
    ]
)
logger = logging.getLogger(__name__)

def check_environment():
    """Check if required environment variables are set"""
    logger.info("Checking environment variables...")
    
    required_vars = [
        'DHAN_ACCESS_TOKEN', 
        'DHAN_CLIENT_ID',
        'SUPABASE_URL',
        'SUPABASE_KEY'
    ]
    
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        logger.error(f"Missing required environment variables: {', '.join(missing_vars)}")
        logger.error("Please set these variables in your .env file")
        return False
    
    logger.info("All required environment variables are set")
    return True

def setup_database():
    """Set up database schema"""
    logger.info("Setting up database schema...")
    
    try:
        from setup_db_schema import setup_database as setup_db
        result = setup_db()
        
        if result:
            logger.info("Database schema successfully set up")
            return True
        else:
            logger.error("Failed to set up database schema")
            return False
    except Exception as e:
        logger.error(f"Error setting up database schema: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def initialize_config():
    """Initialize configuration in database"""
    logger.info("Initializing configuration...")
    
    try:
        from admin_api import AdminAPI
        result = AdminAPI.initialize_from_config()
        
        if isinstance(result, dict) and 'error' in result:
            logger.error(f"Failed to initialize configuration: {result['error']}")
            return False
        
        logger.info("Configuration successfully initialized")
        return True
    except Exception as e:
        logger.error(f"Error initializing configuration: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def start_data_collection(run_once=False):
    """Start data collection process"""
    logger.info("Starting data collection process...")
    
    try:
        from options_manager import OptionsManager
        
        if run_once:
            # Run once and exit
            logger.info("Running data collection once...")
            OptionsManager.fetch_all_options_data()
            logger.info("Data collection completed")
            return True
        else:
            # Run continuously with scheduler
            logger.info("Starting scheduled data collection...")
            OptionsManager.setup_schedule()
            # Note: This call is blocking and will not return unless interrupted
            return True
    except Exception as e:
        logger.error(f"Error starting data collection: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def main():
    """Main entry point for initialization"""
    parser = argparse.ArgumentParser(description='Initialize Options Chain Data Management System')
    parser.add_argument('--setup-only', action='store_true', help='Only set up database, do not start collection')
    parser.add_argument('--run-once', action='store_true', help='Run data collection once and exit')
    parser.add_argument('--skip-db-setup', action='store_true', help='Skip database setup')
    args = parser.parse_args()
    
    # Load environment variables
    load_dotenv()
    
    # Check environment
    if not check_environment():
        return 1
    
    # Set up database
    if not args.skip_db_setup:
        if not setup_database():
            logger.error("Database setup failed, aborting initialization")
            return 1
    else:
        logger.info("Skipping database setup as requested")
    
    # Initialize configuration
    if not initialize_config():
        logger.error("Configuration initialization failed, aborting initialization")
        return 1
    
    # Start data collection if not setup-only
    if not args.setup_only:
        if not start_data_collection(run_once=args.run_once):
            logger.error("Data collection failed to start")
            return 1
    else:
        logger.info("Setup completed, skipping data collection as requested")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())