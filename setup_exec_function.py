import os
import requests
from dotenv import load_dotenv
import logging

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

def setup_exec_function():
    try:
        logger.info("Setting up exec_sql function...")
        
        # Read the SQL file
        with open('create_exec_function.sql', 'r') as file:
            sql = file.read()
        
        # Execute the SQL directly using the REST API
        url = f"{os.getenv('SUPABASE_URL')}/rest/v1/rpc/exec_sql"
        headers = {
            'apikey': os.getenv('SUPABASE_KEY'),
            'Authorization': f"Bearer {os.getenv('SUPABASE_KEY')}",
            'Content-Type': 'application/json',
            'Prefer': 'return=minimal'
        }
        data = {'sql_command': sql}
        
        response = requests.post(url, headers=headers, json=data)
        
        if response.status_code == 200:
            logger.info("Successfully created exec_sql function")
        else:
            logger.error(f"Error creating exec_sql function: {response.text}")
        
    except Exception as e:
        logger.error(f"Error setting up exec_sql function: {e}")
        raise

if __name__ == "__main__":
    setup_exec_function() 