[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"

[project]
name = "db_manager"
version = "0.1.0"
authors = [{ name = "Your Name", email = "<EMAIL>" }]
description = "A service to fetch and store NIFTY options chain data from DhanHQ into a PostgreSQL database"
readme = "README.md"
requires-python = ">=3.8"
classifiers = [
    "Programming Language :: Python :: 3",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
]
dependencies = [
    "requests~=2.31.0",
    "pandas~=2.1.0",
    "python-dotenv~=1.0.0",
    "psycopg2-binary~=2.9.9",
]

[project.optional-dependencies]
dev = ["pytest", "black"]

[project.urls]
Homepage = "https://github.com/yourusername/db_manager"
Issues = "https://github.com/yourusername/db_manager/issues"
