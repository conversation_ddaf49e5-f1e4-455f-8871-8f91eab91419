"""
Option Chain Data Management Configuration

This configuration file contains settings specifically for managing option chain data
across multiple assets. This includes which assets to track, API-specific settings,
and various configuration options for data collection and storage.
"""

# Assets with option chain support
# Each asset has the necessary metadata for fetching option chain data
OPTION_CHAIN_ASSETS = {
    'NIFTY': {
        'security_id': 13,  # Nifty security ID for DhanHQ
        'exchange_segment': 'IDX_I',
        'lot_size': 50,
        'name': 'NIFTY 50',
        'description': 'National Stock Exchange Nifty 50 Index',
        'is_active': True,
        'priority': 1  # Lower number means higher priority
    },
    'BANKNIFTY': {
        'security_id': 11,  # BankNifty security ID for DhanHQ
        'exchange_segment': 'IDX_I',
        'lot_size': 25,
        'name': 'NIFTY BANK',
        'description': 'National Stock Exchange Nifty Bank Index',
        'is_active': True,
        'priority': 2
    },
    'FINNIFTY': {
        'security_id': 41,  # FinNifty security ID for DhanHQ
        'exchange_segment': 'IDX_I',
        'lot_size': 40,
        'name': 'NIFTY FIN SERVICE',
        'description': 'National Stock Exchange Nifty Financial Services Index',
        'is_active': True,
        'priority': 3
    },
    'SENSEX': {
        'security_id': 17,  # Sensex security ID for DhanHQ
        'exchange_segment': 'IDX_I',
        'lot_size': 10,
        'name': 'BSE SENSEX',
        'description': 'Bombay Stock Exchange Sensex Index',
        'is_active': False,  # Default inactive
        'priority': 4
    },
    'MIDCPNIFTY': {
        'security_id': 83,  # MidcapNifty security ID for DhanHQ
        'exchange_segment': 'IDX_I',
        'lot_size': 75,
        'name': 'NIFTY MIDCAP SELECT',
        'description': 'National Stock Exchange Nifty Midcap Select Index',
        'is_active': True,
        'priority': 5
    }
}

# Option Chain Data Collection Settings
OPTION_CHAIN_SETTINGS = {
    'data_refresh_interval_seconds': 5,   # How often to fetch new option chain data
    'expiry_refresh_interval_minutes': 30, # How often to check for new expiry dates
    'max_expiries_to_fetch': 5,            # Maximum number of expiry dates to fetch per asset
    'fetch_monthly_expiries': True,        # Whether to fetch monthly expiry dates
    'fetch_weekly_expiries': True,         # Whether to fetch weekly expiry dates
    'collect_greeks': True,                # Whether to collect greeks data
    'store_raw_data': True                 # Whether to store raw API response
}

# Admin Settings
ADMIN_SETTINGS = {
    'enable_admin_ui': True,              # Whether to enable admin UI
    'admin_access_roles': ['admin'],      # Supabase roles with admin access
    'allow_asset_management': True,       # Allow adding/removing assets
    'allow_schedule_changes': True,       # Allow changing schedule settings
    'allow_database_operations': True,    # Allow database operations (cleanup, etc.)
    'max_concurrent_assets': 5,           # Maximum number of assets to fetch concurrently
    'require_auth_for_data_access': False # Whether authentication is required for data access
}

# Database Tables Configuration
DATABASE_CONFIG = {
    'options_table_prefix': 'options_',    # Prefix for options data tables
    'raw_data_table_prefix': 'raw_',       # Prefix for raw data tables
    'batch_size': 1000,                    # Number of records to insert in one batch
    'cleanup_days': 30,                    # Number of days to keep historical data
    'backup_enabled': True,                # Whether to create backup files
    'compress_old_data': True              # Whether to compress old data
}

# Default Asset (used if none specified)
DEFAULT_ASSET = 'NIFTY'

# Get active assets list
def get_active_assets():
    """Return a dictionary of active assets for option chain data collection"""
    return {k: v for k, v in OPTION_CHAIN_ASSETS.items() if v.get('is_active', False)}

# Get sorted assets by priority
def get_assets_by_priority():
    """Return a list of asset keys sorted by priority"""
    return sorted(OPTION_CHAIN_ASSETS.keys(), 
                  key=lambda x: OPTION_CHAIN_ASSETS[x].get('priority', 999)) 