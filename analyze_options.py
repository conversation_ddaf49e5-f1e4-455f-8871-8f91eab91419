import os
import json
import pandas as pd
import matplotlib.pyplot as plt
from dotenv import load_dotenv
from datetime import datetime, timedelta
import pytz
from supabase import create_client, Client

# Load environment variables
load_dotenv()

# Supabase credentials
SUPABASE_URL = os.getenv('SUPABASE_URL')
SUPABASE_KEY = os.getenv('SUPABASE_KEY')

# Initialize Supabase client
supabase: Client = create_client(
    SUPABASE_URL,
    SUPABASE_KEY
)

def fetch_options_data(expiry_date=None, start_date=None, end_date=None, limit=10):
    """
    Fetch raw options data from the database
    
    Args:
        expiry_date (str, optional): Specific expiry date to query
        start_date (str, optional): Start date for time-range query (ISO format)
        end_date (str, optional): End date for time-range query (ISO format)
        limit (int, optional): Maximum number of records to return
        
    Returns:
        list: List of raw options data records
    """
    try:
        query = supabase.table('nifty_options_raw').select('*')
        
        # Apply filters if provided
        if expiry_date:
            query = query.eq('expiry_date', expiry_date)
        
        if start_date:
            query = query.gte('created_at', start_date)
            
        if end_date:
            query = query.lte('created_at', end_date)
        
        # Order by creation time (newest first)
        query = query.order('created_at', desc=True)
        
        # Limit results
        if limit:
            query = query.limit(limit)
            
        response = query.execute()
        
        if response.data:
            print(f"Retrieved {len(response.data)} raw options data records")
            return response.data
        else:
            print("No raw options data found with the provided filters")
            return []
    except Exception as e:
        print(f"Error fetching raw options data: {e}")
        return []

def get_available_expiry_dates():
    """Get all available expiry dates from the database"""
    try:
        response = supabase.table('expiry_dates').select('expiry_date').execute()
        
        if response.data:
            return [record['expiry_date'] for record in response.data]
        return []
    except Exception as e:
        print(f"Error fetching expiry dates: {e}")
        return []

def extract_spot_price_history(records):
    """Extract spot price history from multiple records"""
    spot_prices = []
    
    for record in records:
        created_at = record.get('created_at')
        spot_price = record.get('spot_price')
        
        if created_at and spot_price:
            spot_prices.append({
                'timestamp': created_at,
                'spot_price': spot_price
            })
    
    return pd.DataFrame(spot_prices)

def extract_option_data(records, strike_price, option_type='CE'):
    """
    Extract specific option data from multiple records
    
    Args:
        records: List of raw options data records
        strike_price: Strike price to extract
        option_type: 'CE' or 'PE'
        
    Returns:
        DataFrame with option data time series
    """
    option_data = []
    
    for record in records:
        created_at = record.get('created_at')
        raw_data = record.get('raw_data')
        
        if not created_at or not raw_data:
            continue
            
        try:
            # Navigate the nested JSON structure
            options_data = raw_data.get('data', {}).get('data', {})
            oc_data = options_data.get('oc', {})
            
            # Find the strike price
            strike_key = f"{strike_price}.000000"
            strike_data = oc_data.get(strike_key, {})
            
            # Get the option data for the specified type
            opt_type = option_type.lower()
            option = strike_data.get(opt_type, {})
            
            if option:
                data = {
                    'timestamp': created_at,
                    'strike_price': strike_price,
                    'option_type': option_type,
                    'last_price': float(option.get('last_price', 0) or 0),
                    'volume': int(option.get('volume', 0) or 0),
                    'oi': int(option.get('oi', 0) or 0),
                    'iv': float(option.get('implied_volatility', 0) or 0)
                }
                
                # Add greeks if available
                greeks = option.get('greeks', {})
                if greeks:
                    data.update({
                        'delta': float(greeks.get('delta', 0) or 0),
                        'gamma': float(greeks.get('gamma', 0) or 0),
                        'theta': float(greeks.get('theta', 0) or 0),
                        'vega': float(greeks.get('vega', 0) or 0)
                    })
                
                option_data.append(data)
                
        except Exception as e:
            print(f"Error processing record: {e}")
    
    return pd.DataFrame(option_data)

def plot_spot_price(df):
    """Plot spot price history"""
    if df.empty:
        print("No data to plot")
        return
        
    plt.figure(figsize=(12, 6))
    plt.plot(pd.to_datetime(df['timestamp']), df['spot_price'])
    plt.title('Spot Price History')
    plt.xlabel('Time')
    plt.ylabel('Spot Price')
    plt.grid(True)
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.show()

def plot_option_metrics(df, metric='last_price'):
    """Plot option metrics over time"""
    if df.empty:
        print("No data to plot")
        return
        
    plt.figure(figsize=(12, 6))
    plt.plot(pd.to_datetime(df['timestamp']), df[metric])
    plt.title(f'Option {metric.replace("_", " ").title()} History')
    plt.xlabel('Time')
    plt.ylabel(metric.replace('_', ' ').title())
    plt.grid(True)
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.show()

def plot_iv_surface(records, expiry_date):
    """
    Plot implied volatility surface for a specific expiry date
    
    Args:
        records: Raw options data record
        expiry_date: Expiry date for IV surface
    """
    # Use the most recent record
    if not records:
        print("No data to plot")
        return
        
    record = records[0]
    raw_data = record.get('raw_data')
    
    if not raw_data:
        print("No raw data in record")
        return
        
    try:
        # Extract data
        options_data = raw_data.get('data', {}).get('data', {})
        spot_price = options_data.get('last_price', 0)
        oc_data = options_data.get('oc', {})
        
        # Prepare data for IV surface
        strikes = []
        ce_ivs = []
        pe_ivs = []
        
        for strike_key, strike_data in oc_data.items():
            try:
                strike_price = float(strike_key.replace('.000000', ''))
                ce_iv = strike_data.get('ce', {}).get('implied_volatility', 0) or 0
                pe_iv = strike_data.get('pe', {}).get('implied_volatility', 0) or 0
                
                strikes.append(strike_price)
                ce_ivs.append(ce_iv)
                pe_ivs.append(pe_iv)
            except:
                continue
        
        # Create DataFrame
        iv_df = pd.DataFrame({
            'Strike': strikes,
            'CE_IV': ce_ivs,
            'PE_IV': pe_ivs
        })
        
        # Sort by strike price
        iv_df = iv_df.sort_values('Strike')
        
        # Plot
        plt.figure(figsize=(12, 6))
        plt.plot(iv_df['Strike'], iv_df['CE_IV'], 'b-', label='Call IV')
        plt.plot(iv_df['Strike'], iv_df['PE_IV'], 'r-', label='Put IV')
        plt.axvline(x=spot_price, color='g', linestyle='--', label=f'Spot Price: {spot_price}')
        plt.title(f'Implied Volatility Smile for {expiry_date}')
        plt.xlabel('Strike Price')
        plt.ylabel('Implied Volatility (%)')
        plt.legend()
        plt.grid(True)
        plt.tight_layout()
        plt.show()
        
    except Exception as e:
        print(f"Error plotting IV surface: {e}")

if __name__ == "__main__":
    # Example usage
    print("Available expiry dates:")
    expiry_dates = get_available_expiry_dates()
    for date in expiry_dates:
        print(date)
    
    # Get data for a specific expiry date
    if expiry_dates:
        example_expiry = expiry_dates[0]
        print(f"\nFetching data for expiry date: {example_expiry}")
        
        # Get multiple records to analyze time series
        records = fetch_options_data(
            expiry_date=example_expiry,
            limit=10  # Last 10 records
        )
        
        if records:
            # Extract and plot spot price history
            spot_df = extract_spot_price_history(records)
            if not spot_df.empty:
                print("\nSpot price history:")
                print(spot_df)
                plot_spot_price(spot_df)
            
            # Extract and plot data for ATM call option
            atm_strike = round(records[0].get('spot_price') / 100) * 100
            print(f"\nATM strike: {atm_strike}")
            
            call_df = extract_option_data(records, atm_strike, 'CE')
            if not call_df.empty:
                print("\nATM call option data:")
                print(call_df)
                plot_option_metrics(call_df, 'last_price')
                plot_option_metrics(call_df, 'iv')
            
            # Plot IV surface
            print("\nPlotting IV surface...")
            plot_iv_surface(records, example_expiry)
    else:
        print("No expiry dates available") 