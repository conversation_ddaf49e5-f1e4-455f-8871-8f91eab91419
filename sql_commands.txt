   SELECT expiry_date, strike_price, call_price, put_price, call_oi, put_oi, call_iv, put_iv...
      SELECT time_bucket, expiry_date, strike_price, avg_iv, max_iv, min_iv...
         SELECT strike_price, oi, previous_oi, oi_change...


              // Using option_chain_view
     const optionChainData = await supabase
       .from('option_chain_view')
       .select('*')
       .eq('expiry_date', selectedExpiry);


            // Using historical_iv_view
     const ivData = await supabase
       .from('historical_iv_view')
       .select('*')
       .eq('expiry_date', selectedExpiry)
       .order('time_bucket', { ascending: false });


            // Using oi_analysis_view
     const oiData = await supabase
       .from('oi_analysis_view')
       .select('*')
       .eq('expiry_date', selectedExpiry);