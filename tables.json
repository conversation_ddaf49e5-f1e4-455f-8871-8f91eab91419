[{"id": 29250, "schema": "pgsodium", "name": "key", "rls_enabled": false, "rls_forced": false, "replica_identity": "DEFAULT", "bytes": 49152, "size": "48 kB", "live_rows_estimate": 0, "dead_rows_estimate": 1, "comment": "This table holds metadata for derived keys given a key_id and key_context. The raw key is never stored.", "primary_keys": [{"name": "id", "schema": "pgsodium", "table_id": 29250, "table_name": "key"}], "relationships": [{"id": 29511, "source_schema": "vault", "constraint_name": "secrets_key_id_fkey", "source_table_name": "secrets", "target_table_name": "key", "source_column_name": "key_id", "target_column_name": "id", "target_table_schema": "pgsodium"}, {"id": 29317, "source_schema": "pgsodium", "constraint_name": "key_parent_key_fkey", "source_table_name": "key", "target_table_name": "key", "source_column_name": "parent_key", "target_column_name": "id", "target_table_schema": "pgsodium"}], "columns": [{"table_id": 29250, "schema": "pgsodium", "table": "key", "id": "29250.1", "ordinal_position": 1, "name": "id", "default_value": "gen_random_uuid()", "data_type": "uuid", "format": "uuid", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29250, "schema": "pgsodium", "table": "key", "id": "29250.2", "ordinal_position": 2, "name": "status", "default_value": "'valid'::pgsodium.key_status", "data_type": "USER-DEFINED", "format": "key_status", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": ["default", "valid", "invalid", "expired"], "comment": null}, {"table_id": 29250, "schema": "pgsodium", "table": "key", "id": "29250.3", "ordinal_position": 3, "name": "created", "default_value": "CURRENT_TIMESTAMP", "data_type": "timestamp with time zone", "format": "timestamptz", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29250, "schema": "pgsodium", "table": "key", "id": "29250.4", "ordinal_position": 4, "name": "expires", "default_value": null, "data_type": "timestamp with time zone", "format": "timestamptz", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29250, "schema": "pgsodium", "table": "key", "id": "29250.5", "ordinal_position": 5, "name": "key_type", "default_value": null, "data_type": "USER-DEFINED", "format": "key_type", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": ["aead-ietf", "aead-det", "hmacsha512", "hmacsha256", "auth", "<PERSON><PERSON>h", "<PERSON><PERSON><PERSON>", "kdf", "secretbox", "secretstream", "stream_xchacha20"], "comment": null}, {"table_id": 29250, "schema": "pgsodium", "table": "key", "id": "29250.6", "ordinal_position": 6, "name": "key_id", "default_value": "nextval('pgsodium.key_key_id_seq'::regclass)", "data_type": "bigint", "format": "int8", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29250, "schema": "pgsodium", "table": "key", "id": "29250.7", "ordinal_position": 7, "name": "key_context", "default_value": "'\\x7067736f6469756d'::bytea", "data_type": "bytea", "format": "bytea", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": "length(key_context) = 8", "enums": [], "comment": null}, {"table_id": 29250, "schema": "pgsodium", "table": "key", "id": "29250.8", "ordinal_position": 8, "name": "name", "default_value": null, "data_type": "text", "format": "text", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": true, "check": null, "enums": [], "comment": null}, {"table_id": 29250, "schema": "pgsodium", "table": "key", "id": "29250.9", "ordinal_position": 9, "name": "associated_data", "default_value": "'associated'::text", "data_type": "text", "format": "text", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29250, "schema": "pgsodium", "table": "key", "id": "29250.10", "ordinal_position": 10, "name": "raw_key", "default_value": null, "data_type": "bytea", "format": "bytea", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29250, "schema": "pgsodium", "table": "key", "id": "29250.11", "ordinal_position": 11, "name": "raw_key_nonce", "default_value": null, "data_type": "bytea", "format": "bytea", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29250, "schema": "pgsodium", "table": "key", "id": "29250.12", "ordinal_position": 12, "name": "parent_key", "default_value": null, "data_type": "uuid", "format": "uuid", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29250, "schema": "pgsodium", "table": "key", "id": "29250.13", "ordinal_position": 13, "name": "comment", "default_value": null, "data_type": "text", "format": "text", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29250, "schema": "pgsodium", "table": "key", "id": "29250.14", "ordinal_position": 14, "name": "user_data", "default_value": null, "data_type": "text", "format": "text", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}]}, {"id": 29498, "schema": "vault", "name": "secrets", "rls_enabled": false, "rls_forced": false, "replica_identity": "DEFAULT", "bytes": 24576, "size": "24 kB", "live_rows_estimate": 0, "dead_rows_estimate": 0, "comment": "Table with encrypted `secret` column for storing sensitive information on disk.", "primary_keys": [{"name": "id", "schema": "vault", "table_id": 29498, "table_name": "secrets"}], "relationships": [{"id": 29511, "source_schema": "vault", "constraint_name": "secrets_key_id_fkey", "source_table_name": "secrets", "target_table_name": "key", "source_column_name": "key_id", "target_column_name": "id", "target_table_schema": "pgsodium"}], "columns": [{"table_id": 29498, "schema": "vault", "table": "secrets", "id": "29498.1", "ordinal_position": 1, "name": "id", "default_value": "gen_random_uuid()", "data_type": "uuid", "format": "uuid", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29498, "schema": "vault", "table": "secrets", "id": "29498.2", "ordinal_position": 2, "name": "name", "default_value": null, "data_type": "text", "format": "text", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29498, "schema": "vault", "table": "secrets", "id": "29498.3", "ordinal_position": 3, "name": "description", "default_value": "''::text", "data_type": "text", "format": "text", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29498, "schema": "vault", "table": "secrets", "id": "29498.4", "ordinal_position": 4, "name": "secret", "default_value": null, "data_type": "text", "format": "text", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29498, "schema": "vault", "table": "secrets", "id": "29498.5", "ordinal_position": 5, "name": "key_id", "default_value": "(pgsodium.create_key()).id", "data_type": "uuid", "format": "uuid", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29498, "schema": "vault", "table": "secrets", "id": "29498.6", "ordinal_position": 6, "name": "nonce", "default_value": "pgsodium.crypto_aead_det_noncegen()", "data_type": "bytea", "format": "bytea", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29498, "schema": "vault", "table": "secrets", "id": "29498.7", "ordinal_position": 7, "name": "created_at", "default_value": "CURRENT_TIMESTAMP", "data_type": "timestamp with time zone", "format": "timestamptz", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29498, "schema": "vault", "table": "secrets", "id": "29498.8", "ordinal_position": 8, "name": "updated_at", "default_value": "CURRENT_TIMESTAMP", "data_type": "timestamp with time zone", "format": "timestamptz", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}]}, {"id": 29648, "schema": "auth", "name": "audit_log_entries", "rls_enabled": true, "rls_forced": false, "replica_identity": "DEFAULT", "bytes": 81920, "size": "80 kB", "live_rows_estimate": 38, "dead_rows_estimate": 0, "comment": "Auth: Audit trail for user actions.", "primary_keys": [{"name": "id", "schema": "auth", "table_id": 29648, "table_name": "audit_log_entries"}], "relationships": [], "columns": [{"table_id": 29648, "schema": "auth", "table": "audit_log_entries", "id": "29648.1", "ordinal_position": 1, "name": "instance_id", "default_value": null, "data_type": "uuid", "format": "uuid", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29648, "schema": "auth", "table": "audit_log_entries", "id": "29648.2", "ordinal_position": 2, "name": "id", "default_value": null, "data_type": "uuid", "format": "uuid", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29648, "schema": "auth", "table": "audit_log_entries", "id": "29648.3", "ordinal_position": 3, "name": "payload", "default_value": null, "data_type": "json", "format": "json", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29648, "schema": "auth", "table": "audit_log_entries", "id": "29648.4", "ordinal_position": 4, "name": "created_at", "default_value": null, "data_type": "timestamp with time zone", "format": "timestamptz", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29648, "schema": "auth", "table": "audit_log_entries", "id": "29648.5", "ordinal_position": 5, "name": "ip_address", "default_value": "''::character varying", "data_type": "character varying", "format": "<PERSON><PERSON><PERSON>", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}]}, {"id": 29654, "schema": "auth", "name": "flow_state", "rls_enabled": true, "rls_forced": false, "replica_identity": "DEFAULT", "bytes": 81920, "size": "80 kB", "live_rows_estimate": 2, "dead_rows_estimate": 2, "comment": "stores metadata for pkce logins", "primary_keys": [{"name": "id", "schema": "auth", "table_id": 29654, "table_name": "flow_state"}], "relationships": [{"id": 29951, "source_schema": "auth", "constraint_name": "saml_relay_states_flow_state_id_fkey", "source_table_name": "saml_relay_states", "target_table_name": "flow_state", "source_column_name": "flow_state_id", "target_column_name": "id", "target_table_schema": "auth"}], "columns": [{"table_id": 29654, "schema": "auth", "table": "flow_state", "id": "29654.1", "ordinal_position": 1, "name": "id", "default_value": null, "data_type": "uuid", "format": "uuid", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29654, "schema": "auth", "table": "flow_state", "id": "29654.2", "ordinal_position": 2, "name": "user_id", "default_value": null, "data_type": "uuid", "format": "uuid", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29654, "schema": "auth", "table": "flow_state", "id": "29654.3", "ordinal_position": 3, "name": "auth_code", "default_value": null, "data_type": "text", "format": "text", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29654, "schema": "auth", "table": "flow_state", "id": "29654.4", "ordinal_position": 4, "name": "code_challenge_method", "default_value": null, "data_type": "USER-DEFINED", "format": "code_challenge_method", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": ["s256", "plain"], "comment": null}, {"table_id": 29654, "schema": "auth", "table": "flow_state", "id": "29654.5", "ordinal_position": 5, "name": "code_challenge", "default_value": null, "data_type": "text", "format": "text", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29654, "schema": "auth", "table": "flow_state", "id": "29654.6", "ordinal_position": 6, "name": "provider_type", "default_value": null, "data_type": "text", "format": "text", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29654, "schema": "auth", "table": "flow_state", "id": "29654.7", "ordinal_position": 7, "name": "provider_access_token", "default_value": null, "data_type": "text", "format": "text", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29654, "schema": "auth", "table": "flow_state", "id": "29654.8", "ordinal_position": 8, "name": "provider_refresh_token", "default_value": null, "data_type": "text", "format": "text", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29654, "schema": "auth", "table": "flow_state", "id": "29654.9", "ordinal_position": 9, "name": "created_at", "default_value": null, "data_type": "timestamp with time zone", "format": "timestamptz", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29654, "schema": "auth", "table": "flow_state", "id": "29654.10", "ordinal_position": 10, "name": "updated_at", "default_value": null, "data_type": "timestamp with time zone", "format": "timestamptz", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29654, "schema": "auth", "table": "flow_state", "id": "29654.11", "ordinal_position": 11, "name": "authentication_method", "default_value": null, "data_type": "text", "format": "text", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29654, "schema": "auth", "table": "flow_state", "id": "29654.12", "ordinal_position": 12, "name": "auth_code_issued_at", "default_value": null, "data_type": "timestamp with time zone", "format": "timestamptz", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}]}, {"id": 29659, "schema": "auth", "name": "identities", "rls_enabled": true, "rls_forced": false, "replica_identity": "DEFAULT", "bytes": 81920, "size": "80 kB", "live_rows_estimate": 1, "dead_rows_estimate": 1, "comment": "Auth: Stores identities associated to a user.", "primary_keys": [{"name": "id", "schema": "auth", "table_id": 29659, "table_name": "identities"}], "relationships": [{"id": 29916, "source_schema": "auth", "constraint_name": "identities_user_id_fkey", "source_table_name": "identities", "target_table_name": "users", "source_column_name": "user_id", "target_column_name": "id", "target_table_schema": "auth"}], "columns": [{"table_id": 29659, "schema": "auth", "table": "identities", "id": "29659.1", "ordinal_position": 1, "name": "provider_id", "default_value": null, "data_type": "text", "format": "text", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29659, "schema": "auth", "table": "identities", "id": "29659.2", "ordinal_position": 2, "name": "user_id", "default_value": null, "data_type": "uuid", "format": "uuid", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29659, "schema": "auth", "table": "identities", "id": "29659.3", "ordinal_position": 3, "name": "identity_data", "default_value": null, "data_type": "jsonb", "format": "jsonb", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29659, "schema": "auth", "table": "identities", "id": "29659.4", "ordinal_position": 4, "name": "provider", "default_value": null, "data_type": "text", "format": "text", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29659, "schema": "auth", "table": "identities", "id": "29659.5", "ordinal_position": 5, "name": "last_sign_in_at", "default_value": null, "data_type": "timestamp with time zone", "format": "timestamptz", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29659, "schema": "auth", "table": "identities", "id": "29659.6", "ordinal_position": 6, "name": "created_at", "default_value": null, "data_type": "timestamp with time zone", "format": "timestamptz", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29659, "schema": "auth", "table": "identities", "id": "29659.7", "ordinal_position": 7, "name": "updated_at", "default_value": null, "data_type": "timestamp with time zone", "format": "timestamptz", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29659, "schema": "auth", "table": "identities", "id": "29659.8", "ordinal_position": 8, "name": "email", "default_value": "lower((identity_data ->> 'email'::text))", "data_type": "text", "format": "text", "is_identity": false, "identity_generation": null, "is_generated": true, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": "Auth: Email is a generated column that references the optional email property in the identity_data"}, {"table_id": 29659, "schema": "auth", "table": "identities", "id": "29659.9", "ordinal_position": 9, "name": "id", "default_value": "gen_random_uuid()", "data_type": "uuid", "format": "uuid", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}]}, {"id": 29666, "schema": "auth", "name": "instances", "rls_enabled": true, "rls_forced": false, "replica_identity": "DEFAULT", "bytes": 16384, "size": "16 kB", "live_rows_estimate": 0, "dead_rows_estimate": 0, "comment": "Auth: Manages users across multiple sites.", "primary_keys": [{"name": "id", "schema": "auth", "table_id": 29666, "table_name": "instances"}], "relationships": [], "columns": [{"table_id": 29666, "schema": "auth", "table": "instances", "id": "29666.1", "ordinal_position": 1, "name": "id", "default_value": null, "data_type": "uuid", "format": "uuid", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29666, "schema": "auth", "table": "instances", "id": "29666.2", "ordinal_position": 2, "name": "uuid", "default_value": null, "data_type": "uuid", "format": "uuid", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29666, "schema": "auth", "table": "instances", "id": "29666.3", "ordinal_position": 3, "name": "raw_base_config", "default_value": null, "data_type": "text", "format": "text", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29666, "schema": "auth", "table": "instances", "id": "29666.4", "ordinal_position": 4, "name": "created_at", "default_value": null, "data_type": "timestamp with time zone", "format": "timestamptz", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29666, "schema": "auth", "table": "instances", "id": "29666.5", "ordinal_position": 5, "name": "updated_at", "default_value": null, "data_type": "timestamp with time zone", "format": "timestamptz", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}]}, {"id": 29671, "schema": "auth", "name": "mfa_amr_claims", "rls_enabled": true, "rls_forced": false, "replica_identity": "DEFAULT", "bytes": 49152, "size": "48 kB", "live_rows_estimate": 2, "dead_rows_estimate": 0, "comment": "auth: stores authenticator method reference claims for multi factor authentication", "primary_keys": [{"name": "id", "schema": "auth", "table_id": 29671, "table_name": "mfa_amr_claims"}], "relationships": [{"id": 29921, "source_schema": "auth", "constraint_name": "mfa_amr_claims_session_id_fkey", "source_table_name": "mfa_amr_claims", "target_table_name": "sessions", "source_column_name": "session_id", "target_column_name": "id", "target_table_schema": "auth"}], "columns": [{"table_id": 29671, "schema": "auth", "table": "mfa_amr_claims", "id": "29671.1", "ordinal_position": 1, "name": "session_id", "default_value": null, "data_type": "uuid", "format": "uuid", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29671, "schema": "auth", "table": "mfa_amr_claims", "id": "29671.2", "ordinal_position": 2, "name": "created_at", "default_value": null, "data_type": "timestamp with time zone", "format": "timestamptz", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29671, "schema": "auth", "table": "mfa_amr_claims", "id": "29671.3", "ordinal_position": 3, "name": "updated_at", "default_value": null, "data_type": "timestamp with time zone", "format": "timestamptz", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29671, "schema": "auth", "table": "mfa_amr_claims", "id": "29671.4", "ordinal_position": 4, "name": "authentication_method", "default_value": null, "data_type": "text", "format": "text", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29671, "schema": "auth", "table": "mfa_amr_claims", "id": "29671.5", "ordinal_position": 5, "name": "id", "default_value": null, "data_type": "uuid", "format": "uuid", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}]}, {"id": 29676, "schema": "auth", "name": "mfa_challenges", "rls_enabled": true, "rls_forced": false, "replica_identity": "DEFAULT", "bytes": 24576, "size": "24 kB", "live_rows_estimate": 0, "dead_rows_estimate": 0, "comment": "auth: stores metadata about challenge requests made", "primary_keys": [{"name": "id", "schema": "auth", "table_id": 29676, "table_name": "mfa_challenges"}], "relationships": [{"id": 29926, "source_schema": "auth", "constraint_name": "mfa_challenges_auth_factor_id_fkey", "source_table_name": "mfa_challenges", "target_table_name": "mfa_factors", "source_column_name": "factor_id", "target_column_name": "id", "target_table_schema": "auth"}], "columns": [{"table_id": 29676, "schema": "auth", "table": "mfa_challenges", "id": "29676.1", "ordinal_position": 1, "name": "id", "default_value": null, "data_type": "uuid", "format": "uuid", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29676, "schema": "auth", "table": "mfa_challenges", "id": "29676.2", "ordinal_position": 2, "name": "factor_id", "default_value": null, "data_type": "uuid", "format": "uuid", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29676, "schema": "auth", "table": "mfa_challenges", "id": "29676.3", "ordinal_position": 3, "name": "created_at", "default_value": null, "data_type": "timestamp with time zone", "format": "timestamptz", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29676, "schema": "auth", "table": "mfa_challenges", "id": "29676.4", "ordinal_position": 4, "name": "verified_at", "default_value": null, "data_type": "timestamp with time zone", "format": "timestamptz", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29676, "schema": "auth", "table": "mfa_challenges", "id": "29676.5", "ordinal_position": 5, "name": "ip_address", "default_value": null, "data_type": "inet", "format": "inet", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29676, "schema": "auth", "table": "mfa_challenges", "id": "29676.6", "ordinal_position": 6, "name": "otp_code", "default_value": null, "data_type": "text", "format": "text", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29676, "schema": "auth", "table": "mfa_challenges", "id": "29676.7", "ordinal_position": 7, "name": "web_authn_session_data", "default_value": null, "data_type": "jsonb", "format": "jsonb", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}]}, {"id": 29681, "schema": "auth", "name": "mfa_factors", "rls_enabled": true, "rls_forced": false, "replica_identity": "DEFAULT", "bytes": 57344, "size": "56 kB", "live_rows_estimate": 0, "dead_rows_estimate": 0, "comment": "auth: stores metadata about factors", "primary_keys": [{"name": "id", "schema": "auth", "table_id": 29681, "table_name": "mfa_factors"}], "relationships": [{"id": 29926, "source_schema": "auth", "constraint_name": "mfa_challenges_auth_factor_id_fkey", "source_table_name": "mfa_challenges", "target_table_name": "mfa_factors", "source_column_name": "factor_id", "target_column_name": "id", "target_table_schema": "auth"}, {"id": 29931, "source_schema": "auth", "constraint_name": "mfa_factors_user_id_fkey", "source_table_name": "mfa_factors", "target_table_name": "users", "source_column_name": "user_id", "target_column_name": "id", "target_table_schema": "auth"}], "columns": [{"table_id": 29681, "schema": "auth", "table": "mfa_factors", "id": "29681.1", "ordinal_position": 1, "name": "id", "default_value": null, "data_type": "uuid", "format": "uuid", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29681, "schema": "auth", "table": "mfa_factors", "id": "29681.2", "ordinal_position": 2, "name": "user_id", "default_value": null, "data_type": "uuid", "format": "uuid", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29681, "schema": "auth", "table": "mfa_factors", "id": "29681.3", "ordinal_position": 3, "name": "friendly_name", "default_value": null, "data_type": "text", "format": "text", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29681, "schema": "auth", "table": "mfa_factors", "id": "29681.4", "ordinal_position": 4, "name": "factor_type", "default_value": null, "data_type": "USER-DEFINED", "format": "factor_type", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": ["totp", "webauthn", "phone"], "comment": null}, {"table_id": 29681, "schema": "auth", "table": "mfa_factors", "id": "29681.5", "ordinal_position": 5, "name": "status", "default_value": null, "data_type": "USER-DEFINED", "format": "factor_status", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": ["unverified", "verified"], "comment": null}, {"table_id": 29681, "schema": "auth", "table": "mfa_factors", "id": "29681.6", "ordinal_position": 6, "name": "created_at", "default_value": null, "data_type": "timestamp with time zone", "format": "timestamptz", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29681, "schema": "auth", "table": "mfa_factors", "id": "29681.7", "ordinal_position": 7, "name": "updated_at", "default_value": null, "data_type": "timestamp with time zone", "format": "timestamptz", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29681, "schema": "auth", "table": "mfa_factors", "id": "29681.8", "ordinal_position": 8, "name": "secret", "default_value": null, "data_type": "text", "format": "text", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29681, "schema": "auth", "table": "mfa_factors", "id": "29681.9", "ordinal_position": 9, "name": "phone", "default_value": null, "data_type": "text", "format": "text", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29681, "schema": "auth", "table": "mfa_factors", "id": "29681.10", "ordinal_position": 10, "name": "last_challenged_at", "default_value": null, "data_type": "timestamp with time zone", "format": "timestamptz", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": true, "check": null, "enums": [], "comment": null}, {"table_id": 29681, "schema": "auth", "table": "mfa_factors", "id": "29681.11", "ordinal_position": 11, "name": "web_authn_credential", "default_value": null, "data_type": "jsonb", "format": "jsonb", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29681, "schema": "auth", "table": "mfa_factors", "id": "29681.12", "ordinal_position": 12, "name": "web_authn_aaguid", "default_value": null, "data_type": "uuid", "format": "uuid", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}]}, {"id": 29686, "schema": "auth", "name": "one_time_tokens", "rls_enabled": true, "rls_forced": false, "replica_identity": "DEFAULT", "bytes": 114688, "size": "112 kB", "live_rows_estimate": 0, "dead_rows_estimate": 2, "comment": null, "primary_keys": [{"name": "id", "schema": "auth", "table_id": 29686, "table_name": "one_time_tokens"}], "relationships": [{"id": 29936, "source_schema": "auth", "constraint_name": "one_time_tokens_user_id_fkey", "source_table_name": "one_time_tokens", "target_table_name": "users", "source_column_name": "user_id", "target_column_name": "id", "target_table_schema": "auth"}], "columns": [{"table_id": 29686, "schema": "auth", "table": "one_time_tokens", "id": "29686.1", "ordinal_position": 1, "name": "id", "default_value": null, "data_type": "uuid", "format": "uuid", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29686, "schema": "auth", "table": "one_time_tokens", "id": "29686.2", "ordinal_position": 2, "name": "user_id", "default_value": null, "data_type": "uuid", "format": "uuid", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29686, "schema": "auth", "table": "one_time_tokens", "id": "29686.3", "ordinal_position": 3, "name": "token_type", "default_value": null, "data_type": "USER-DEFINED", "format": "one_time_token_type", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": ["confirmation_token", "reauthentication_token", "recovery_token", "email_change_token_new", "email_change_token_current", "phone_change_token"], "comment": null}, {"table_id": 29686, "schema": "auth", "table": "one_time_tokens", "id": "29686.4", "ordinal_position": 4, "name": "token_hash", "default_value": null, "data_type": "text", "format": "text", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": "char_length(token_hash) > 0", "enums": [], "comment": null}, {"table_id": 29686, "schema": "auth", "table": "one_time_tokens", "id": "29686.5", "ordinal_position": 5, "name": "relates_to", "default_value": null, "data_type": "text", "format": "text", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29686, "schema": "auth", "table": "one_time_tokens", "id": "29686.6", "ordinal_position": 6, "name": "created_at", "default_value": "now()", "data_type": "timestamp without time zone", "format": "timestamp", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29686, "schema": "auth", "table": "one_time_tokens", "id": "29686.7", "ordinal_position": 7, "name": "updated_at", "default_value": "now()", "data_type": "timestamp without time zone", "format": "timestamp", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}]}, {"id": 29694, "schema": "auth", "name": "refresh_tokens", "rls_enabled": true, "rls_forced": false, "replica_identity": "DEFAULT", "bytes": 131072, "size": "128 kB", "live_rows_estimate": 18, "dead_rows_estimate": 16, "comment": "Auth: Store of tokens used to refresh JWT tokens once they expire.", "primary_keys": [{"name": "id", "schema": "auth", "table_id": 29694, "table_name": "refresh_tokens"}], "relationships": [{"id": 29941, "source_schema": "auth", "constraint_name": "refresh_tokens_session_id_fkey", "source_table_name": "refresh_tokens", "target_table_name": "sessions", "source_column_name": "session_id", "target_column_name": "id", "target_table_schema": "auth"}], "columns": [{"table_id": 29694, "schema": "auth", "table": "refresh_tokens", "id": "29694.1", "ordinal_position": 1, "name": "instance_id", "default_value": null, "data_type": "uuid", "format": "uuid", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29694, "schema": "auth", "table": "refresh_tokens", "id": "29694.2", "ordinal_position": 2, "name": "id", "default_value": "nextval('auth.refresh_tokens_id_seq'::regclass)", "data_type": "bigint", "format": "int8", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29694, "schema": "auth", "table": "refresh_tokens", "id": "29694.3", "ordinal_position": 3, "name": "token", "default_value": null, "data_type": "character varying", "format": "<PERSON><PERSON><PERSON>", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": true, "check": null, "enums": [], "comment": null}, {"table_id": 29694, "schema": "auth", "table": "refresh_tokens", "id": "29694.4", "ordinal_position": 4, "name": "user_id", "default_value": null, "data_type": "character varying", "format": "<PERSON><PERSON><PERSON>", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29694, "schema": "auth", "table": "refresh_tokens", "id": "29694.5", "ordinal_position": 5, "name": "revoked", "default_value": null, "data_type": "boolean", "format": "bool", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29694, "schema": "auth", "table": "refresh_tokens", "id": "29694.6", "ordinal_position": 6, "name": "created_at", "default_value": null, "data_type": "timestamp with time zone", "format": "timestamptz", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29694, "schema": "auth", "table": "refresh_tokens", "id": "29694.7", "ordinal_position": 7, "name": "updated_at", "default_value": null, "data_type": "timestamp with time zone", "format": "timestamptz", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29694, "schema": "auth", "table": "refresh_tokens", "id": "29694.8", "ordinal_position": 8, "name": "parent", "default_value": null, "data_type": "character varying", "format": "<PERSON><PERSON><PERSON>", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29694, "schema": "auth", "table": "refresh_tokens", "id": "29694.9", "ordinal_position": 9, "name": "session_id", "default_value": null, "data_type": "uuid", "format": "uuid", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}]}, {"id": 29700, "schema": "auth", "name": "saml_providers", "rls_enabled": true, "rls_forced": false, "replica_identity": "DEFAULT", "bytes": 32768, "size": "32 kB", "live_rows_estimate": 0, "dead_rows_estimate": 0, "comment": "Auth: Manages SAML Identity Provider connections.", "primary_keys": [{"name": "id", "schema": "auth", "table_id": 29700, "table_name": "saml_providers"}], "relationships": [{"id": 29946, "source_schema": "auth", "constraint_name": "saml_providers_sso_provider_id_fkey", "source_table_name": "saml_providers", "target_table_name": "sso_providers", "source_column_name": "sso_provider_id", "target_column_name": "id", "target_table_schema": "auth"}], "columns": [{"table_id": 29700, "schema": "auth", "table": "saml_providers", "id": "29700.1", "ordinal_position": 1, "name": "id", "default_value": null, "data_type": "uuid", "format": "uuid", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29700, "schema": "auth", "table": "saml_providers", "id": "29700.2", "ordinal_position": 2, "name": "sso_provider_id", "default_value": null, "data_type": "uuid", "format": "uuid", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29700, "schema": "auth", "table": "saml_providers", "id": "29700.3", "ordinal_position": 3, "name": "entity_id", "default_value": null, "data_type": "text", "format": "text", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": true, "check": "char_length(entity_id) > 0", "enums": [], "comment": null}, {"table_id": 29700, "schema": "auth", "table": "saml_providers", "id": "29700.4", "ordinal_position": 4, "name": "metadata_xml", "default_value": null, "data_type": "text", "format": "text", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": "char_length(metadata_xml) > 0", "enums": [], "comment": null}, {"table_id": 29700, "schema": "auth", "table": "saml_providers", "id": "29700.5", "ordinal_position": 5, "name": "metadata_url", "default_value": null, "data_type": "text", "format": "text", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": "metadata_url = NULL::text OR char_length(metadata_url) > 0", "enums": [], "comment": null}, {"table_id": 29700, "schema": "auth", "table": "saml_providers", "id": "29700.6", "ordinal_position": 6, "name": "attribute_mapping", "default_value": null, "data_type": "jsonb", "format": "jsonb", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29700, "schema": "auth", "table": "saml_providers", "id": "29700.7", "ordinal_position": 7, "name": "created_at", "default_value": null, "data_type": "timestamp with time zone", "format": "timestamptz", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29700, "schema": "auth", "table": "saml_providers", "id": "29700.8", "ordinal_position": 8, "name": "updated_at", "default_value": null, "data_type": "timestamp with time zone", "format": "timestamptz", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29700, "schema": "auth", "table": "saml_providers", "id": "29700.9", "ordinal_position": 9, "name": "name_id_format", "default_value": null, "data_type": "text", "format": "text", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}]}, {"id": 29708, "schema": "auth", "name": "saml_relay_states", "rls_enabled": true, "rls_forced": false, "replica_identity": "DEFAULT", "bytes": 40960, "size": "40 kB", "live_rows_estimate": 0, "dead_rows_estimate": 0, "comment": "Auth: Contains SAML Relay State information for each Service Provider initiated login.", "primary_keys": [{"name": "id", "schema": "auth", "table_id": 29708, "table_name": "saml_relay_states"}], "relationships": [{"id": 29956, "source_schema": "auth", "constraint_name": "saml_relay_states_sso_provider_id_fkey", "source_table_name": "saml_relay_states", "target_table_name": "sso_providers", "source_column_name": "sso_provider_id", "target_column_name": "id", "target_table_schema": "auth"}, {"id": 29951, "source_schema": "auth", "constraint_name": "saml_relay_states_flow_state_id_fkey", "source_table_name": "saml_relay_states", "target_table_name": "flow_state", "source_column_name": "flow_state_id", "target_column_name": "id", "target_table_schema": "auth"}], "columns": [{"table_id": 29708, "schema": "auth", "table": "saml_relay_states", "id": "29708.1", "ordinal_position": 1, "name": "id", "default_value": null, "data_type": "uuid", "format": "uuid", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29708, "schema": "auth", "table": "saml_relay_states", "id": "29708.2", "ordinal_position": 2, "name": "sso_provider_id", "default_value": null, "data_type": "uuid", "format": "uuid", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29708, "schema": "auth", "table": "saml_relay_states", "id": "29708.3", "ordinal_position": 3, "name": "request_id", "default_value": null, "data_type": "text", "format": "text", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": "char_length(request_id) > 0", "enums": [], "comment": null}, {"table_id": 29708, "schema": "auth", "table": "saml_relay_states", "id": "29708.4", "ordinal_position": 4, "name": "for_email", "default_value": null, "data_type": "text", "format": "text", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29708, "schema": "auth", "table": "saml_relay_states", "id": "29708.5", "ordinal_position": 5, "name": "redirect_to", "default_value": null, "data_type": "text", "format": "text", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29708, "schema": "auth", "table": "saml_relay_states", "id": "29708.6", "ordinal_position": 6, "name": "created_at", "default_value": null, "data_type": "timestamp with time zone", "format": "timestamptz", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29708, "schema": "auth", "table": "saml_relay_states", "id": "29708.7", "ordinal_position": 7, "name": "updated_at", "default_value": null, "data_type": "timestamp with time zone", "format": "timestamptz", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29708, "schema": "auth", "table": "saml_relay_states", "id": "29708.8", "ordinal_position": 8, "name": "flow_state_id", "default_value": null, "data_type": "uuid", "format": "uuid", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}]}, {"id": 29714, "schema": "auth", "name": "schema_migrations", "rls_enabled": true, "rls_forced": false, "replica_identity": "DEFAULT", "bytes": 24576, "size": "24 kB", "live_rows_estimate": 61, "dead_rows_estimate": 0, "comment": "Auth: Manages updates to the auth system.", "primary_keys": [{"name": "version", "schema": "auth", "table_id": 29714, "table_name": "schema_migrations"}], "relationships": [], "columns": [{"table_id": 29714, "schema": "auth", "table": "schema_migrations", "id": "29714.1", "ordinal_position": 1, "name": "version", "default_value": null, "data_type": "character varying", "format": "<PERSON><PERSON><PERSON>", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}]}, {"id": 29717, "schema": "auth", "name": "sessions", "rls_enabled": true, "rls_forced": false, "replica_identity": "DEFAULT", "bytes": 81920, "size": "80 kB", "live_rows_estimate": 2, "dead_rows_estimate": 16, "comment": "Auth: Stores session data associated to a user.", "primary_keys": [{"name": "id", "schema": "auth", "table_id": 29717, "table_name": "sessions"}], "relationships": [{"id": 29921, "source_schema": "auth", "constraint_name": "mfa_amr_claims_session_id_fkey", "source_table_name": "mfa_amr_claims", "target_table_name": "sessions", "source_column_name": "session_id", "target_column_name": "id", "target_table_schema": "auth"}, {"id": 29961, "source_schema": "auth", "constraint_name": "sessions_user_id_fkey", "source_table_name": "sessions", "target_table_name": "users", "source_column_name": "user_id", "target_column_name": "id", "target_table_schema": "auth"}, {"id": 29941, "source_schema": "auth", "constraint_name": "refresh_tokens_session_id_fkey", "source_table_name": "refresh_tokens", "target_table_name": "sessions", "source_column_name": "session_id", "target_column_name": "id", "target_table_schema": "auth"}], "columns": [{"table_id": 29717, "schema": "auth", "table": "sessions", "id": "29717.1", "ordinal_position": 1, "name": "id", "default_value": null, "data_type": "uuid", "format": "uuid", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29717, "schema": "auth", "table": "sessions", "id": "29717.2", "ordinal_position": 2, "name": "user_id", "default_value": null, "data_type": "uuid", "format": "uuid", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29717, "schema": "auth", "table": "sessions", "id": "29717.3", "ordinal_position": 3, "name": "created_at", "default_value": null, "data_type": "timestamp with time zone", "format": "timestamptz", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29717, "schema": "auth", "table": "sessions", "id": "29717.4", "ordinal_position": 4, "name": "updated_at", "default_value": null, "data_type": "timestamp with time zone", "format": "timestamptz", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29717, "schema": "auth", "table": "sessions", "id": "29717.5", "ordinal_position": 5, "name": "factor_id", "default_value": null, "data_type": "uuid", "format": "uuid", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29717, "schema": "auth", "table": "sessions", "id": "29717.6", "ordinal_position": 6, "name": "aal", "default_value": null, "data_type": "USER-DEFINED", "format": "aal_level", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": ["aal1", "aal2", "aal3"], "comment": null}, {"table_id": 29717, "schema": "auth", "table": "sessions", "id": "29717.7", "ordinal_position": 7, "name": "not_after", "default_value": null, "data_type": "timestamp with time zone", "format": "timestamptz", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": "Auth: Not after is a nullable column that contains a timestamp after which the session should be regarded as expired."}, {"table_id": 29717, "schema": "auth", "table": "sessions", "id": "29717.8", "ordinal_position": 8, "name": "refreshed_at", "default_value": null, "data_type": "timestamp without time zone", "format": "timestamp", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29717, "schema": "auth", "table": "sessions", "id": "29717.9", "ordinal_position": 9, "name": "user_agent", "default_value": null, "data_type": "text", "format": "text", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29717, "schema": "auth", "table": "sessions", "id": "29717.10", "ordinal_position": 10, "name": "ip", "default_value": null, "data_type": "inet", "format": "inet", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29717, "schema": "auth", "table": "sessions", "id": "29717.11", "ordinal_position": 11, "name": "tag", "default_value": null, "data_type": "text", "format": "text", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}]}, {"id": 29722, "schema": "auth", "name": "sso_domains", "rls_enabled": true, "rls_forced": false, "replica_identity": "DEFAULT", "bytes": 32768, "size": "32 kB", "live_rows_estimate": 0, "dead_rows_estimate": 0, "comment": "Auth: Manages SSO email address domain mapping to an SSO Identity Provider.", "primary_keys": [{"name": "id", "schema": "auth", "table_id": 29722, "table_name": "sso_domains"}], "relationships": [{"id": 29966, "source_schema": "auth", "constraint_name": "sso_domains_sso_provider_id_fkey", "source_table_name": "sso_domains", "target_table_name": "sso_providers", "source_column_name": "sso_provider_id", "target_column_name": "id", "target_table_schema": "auth"}], "columns": [{"table_id": 29722, "schema": "auth", "table": "sso_domains", "id": "29722.1", "ordinal_position": 1, "name": "id", "default_value": null, "data_type": "uuid", "format": "uuid", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29722, "schema": "auth", "table": "sso_domains", "id": "29722.2", "ordinal_position": 2, "name": "sso_provider_id", "default_value": null, "data_type": "uuid", "format": "uuid", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29722, "schema": "auth", "table": "sso_domains", "id": "29722.3", "ordinal_position": 3, "name": "domain", "default_value": null, "data_type": "text", "format": "text", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": "char_length(domain) > 0", "enums": [], "comment": null}, {"table_id": 29722, "schema": "auth", "table": "sso_domains", "id": "29722.4", "ordinal_position": 4, "name": "created_at", "default_value": null, "data_type": "timestamp with time zone", "format": "timestamptz", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29722, "schema": "auth", "table": "sso_domains", "id": "29722.5", "ordinal_position": 5, "name": "updated_at", "default_value": null, "data_type": "timestamp with time zone", "format": "timestamptz", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}]}, {"id": 29728, "schema": "auth", "name": "sso_providers", "rls_enabled": true, "rls_forced": false, "replica_identity": "DEFAULT", "bytes": 24576, "size": "24 kB", "live_rows_estimate": 0, "dead_rows_estimate": 0, "comment": "Auth: Manages SSO identity provider information; see saml_providers for SAML.", "primary_keys": [{"name": "id", "schema": "auth", "table_id": 29728, "table_name": "sso_providers"}], "relationships": [{"id": 29946, "source_schema": "auth", "constraint_name": "saml_providers_sso_provider_id_fkey", "source_table_name": "saml_providers", "target_table_name": "sso_providers", "source_column_name": "sso_provider_id", "target_column_name": "id", "target_table_schema": "auth"}, {"id": 29956, "source_schema": "auth", "constraint_name": "saml_relay_states_sso_provider_id_fkey", "source_table_name": "saml_relay_states", "target_table_name": "sso_providers", "source_column_name": "sso_provider_id", "target_column_name": "id", "target_table_schema": "auth"}, {"id": 29966, "source_schema": "auth", "constraint_name": "sso_domains_sso_provider_id_fkey", "source_table_name": "sso_domains", "target_table_name": "sso_providers", "source_column_name": "sso_provider_id", "target_column_name": "id", "target_table_schema": "auth"}], "columns": [{"table_id": 29728, "schema": "auth", "table": "sso_providers", "id": "29728.1", "ordinal_position": 1, "name": "id", "default_value": null, "data_type": "uuid", "format": "uuid", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29728, "schema": "auth", "table": "sso_providers", "id": "29728.2", "ordinal_position": 2, "name": "resource_id", "default_value": null, "data_type": "text", "format": "text", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": "resource_id = NULL::text OR char_length(resource_id) > 0", "enums": [], "comment": "Auth: Uniquely identifies a SSO provider according to a user-chosen resource ID (case insensitive), useful in infrastructure as code."}, {"table_id": 29728, "schema": "auth", "table": "sso_providers", "id": "29728.3", "ordinal_position": 3, "name": "created_at", "default_value": null, "data_type": "timestamp with time zone", "format": "timestamptz", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29728, "schema": "auth", "table": "sso_providers", "id": "29728.4", "ordinal_position": 4, "name": "updated_at", "default_value": null, "data_type": "timestamp with time zone", "format": "timestamptz", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}]}, {"id": 29734, "schema": "auth", "name": "users", "rls_enabled": true, "rls_forced": false, "replica_identity": "DEFAULT", "bytes": 172032, "size": "168 kB", "live_rows_estimate": 1, "dead_rows_estimate": 8, "comment": "Auth: Stores user login data within a secure schema.", "primary_keys": [{"name": "id", "schema": "auth", "table_id": 29734, "table_name": "users"}], "relationships": [{"id": 29931, "source_schema": "auth", "constraint_name": "mfa_factors_user_id_fkey", "source_table_name": "mfa_factors", "target_table_name": "users", "source_column_name": "user_id", "target_column_name": "id", "target_table_schema": "auth"}, {"id": 29961, "source_schema": "auth", "constraint_name": "sessions_user_id_fkey", "source_table_name": "sessions", "target_table_name": "users", "source_column_name": "user_id", "target_column_name": "id", "target_table_schema": "auth"}, {"id": 29936, "source_schema": "auth", "constraint_name": "one_time_tokens_user_id_fkey", "source_table_name": "one_time_tokens", "target_table_name": "users", "source_column_name": "user_id", "target_column_name": "id", "target_table_schema": "auth"}, {"id": 29916, "source_schema": "auth", "constraint_name": "identities_user_id_fkey", "source_table_name": "identities", "target_table_name": "users", "source_column_name": "user_id", "target_column_name": "id", "target_table_schema": "auth"}], "columns": [{"table_id": 29734, "schema": "auth", "table": "users", "id": "29734.1", "ordinal_position": 1, "name": "instance_id", "default_value": null, "data_type": "uuid", "format": "uuid", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29734, "schema": "auth", "table": "users", "id": "29734.2", "ordinal_position": 2, "name": "id", "default_value": null, "data_type": "uuid", "format": "uuid", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29734, "schema": "auth", "table": "users", "id": "29734.3", "ordinal_position": 3, "name": "aud", "default_value": null, "data_type": "character varying", "format": "<PERSON><PERSON><PERSON>", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29734, "schema": "auth", "table": "users", "id": "29734.4", "ordinal_position": 4, "name": "role", "default_value": null, "data_type": "character varying", "format": "<PERSON><PERSON><PERSON>", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29734, "schema": "auth", "table": "users", "id": "29734.5", "ordinal_position": 5, "name": "email", "default_value": null, "data_type": "character varying", "format": "<PERSON><PERSON><PERSON>", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29734, "schema": "auth", "table": "users", "id": "29734.6", "ordinal_position": 6, "name": "encrypted_password", "default_value": null, "data_type": "character varying", "format": "<PERSON><PERSON><PERSON>", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29734, "schema": "auth", "table": "users", "id": "29734.7", "ordinal_position": 7, "name": "email_confirmed_at", "default_value": null, "data_type": "timestamp with time zone", "format": "timestamptz", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29734, "schema": "auth", "table": "users", "id": "29734.8", "ordinal_position": 8, "name": "invited_at", "default_value": null, "data_type": "timestamp with time zone", "format": "timestamptz", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29734, "schema": "auth", "table": "users", "id": "29734.9", "ordinal_position": 9, "name": "confirmation_token", "default_value": null, "data_type": "character varying", "format": "<PERSON><PERSON><PERSON>", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29734, "schema": "auth", "table": "users", "id": "29734.10", "ordinal_position": 10, "name": "confirmation_sent_at", "default_value": null, "data_type": "timestamp with time zone", "format": "timestamptz", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29734, "schema": "auth", "table": "users", "id": "29734.11", "ordinal_position": 11, "name": "recovery_token", "default_value": null, "data_type": "character varying", "format": "<PERSON><PERSON><PERSON>", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29734, "schema": "auth", "table": "users", "id": "29734.12", "ordinal_position": 12, "name": "recovery_sent_at", "default_value": null, "data_type": "timestamp with time zone", "format": "timestamptz", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29734, "schema": "auth", "table": "users", "id": "29734.13", "ordinal_position": 13, "name": "email_change_token_new", "default_value": null, "data_type": "character varying", "format": "<PERSON><PERSON><PERSON>", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29734, "schema": "auth", "table": "users", "id": "29734.14", "ordinal_position": 14, "name": "email_change", "default_value": null, "data_type": "character varying", "format": "<PERSON><PERSON><PERSON>", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29734, "schema": "auth", "table": "users", "id": "29734.15", "ordinal_position": 15, "name": "email_change_sent_at", "default_value": null, "data_type": "timestamp with time zone", "format": "timestamptz", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29734, "schema": "auth", "table": "users", "id": "29734.16", "ordinal_position": 16, "name": "last_sign_in_at", "default_value": null, "data_type": "timestamp with time zone", "format": "timestamptz", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29734, "schema": "auth", "table": "users", "id": "29734.17", "ordinal_position": 17, "name": "raw_app_meta_data", "default_value": null, "data_type": "jsonb", "format": "jsonb", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29734, "schema": "auth", "table": "users", "id": "29734.18", "ordinal_position": 18, "name": "raw_user_meta_data", "default_value": null, "data_type": "jsonb", "format": "jsonb", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29734, "schema": "auth", "table": "users", "id": "29734.19", "ordinal_position": 19, "name": "is_super_admin", "default_value": null, "data_type": "boolean", "format": "bool", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29734, "schema": "auth", "table": "users", "id": "29734.20", "ordinal_position": 20, "name": "created_at", "default_value": null, "data_type": "timestamp with time zone", "format": "timestamptz", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29734, "schema": "auth", "table": "users", "id": "29734.21", "ordinal_position": 21, "name": "updated_at", "default_value": null, "data_type": "timestamp with time zone", "format": "timestamptz", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29734, "schema": "auth", "table": "users", "id": "29734.22", "ordinal_position": 22, "name": "phone", "default_value": "NULL::character varying", "data_type": "text", "format": "text", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": true, "check": null, "enums": [], "comment": null}, {"table_id": 29734, "schema": "auth", "table": "users", "id": "29734.23", "ordinal_position": 23, "name": "phone_confirmed_at", "default_value": null, "data_type": "timestamp with time zone", "format": "timestamptz", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29734, "schema": "auth", "table": "users", "id": "29734.24", "ordinal_position": 24, "name": "phone_change", "default_value": "''::character varying", "data_type": "text", "format": "text", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29734, "schema": "auth", "table": "users", "id": "29734.25", "ordinal_position": 25, "name": "phone_change_token", "default_value": "''::character varying", "data_type": "character varying", "format": "<PERSON><PERSON><PERSON>", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29734, "schema": "auth", "table": "users", "id": "29734.26", "ordinal_position": 26, "name": "phone_change_sent_at", "default_value": null, "data_type": "timestamp with time zone", "format": "timestamptz", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29734, "schema": "auth", "table": "users", "id": "29734.27", "ordinal_position": 27, "name": "confirmed_at", "default_value": "LEAST(email_confirmed_at, phone_confirmed_at)", "data_type": "timestamp with time zone", "format": "timestamptz", "is_identity": false, "identity_generation": null, "is_generated": true, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29734, "schema": "auth", "table": "users", "id": "29734.28", "ordinal_position": 28, "name": "email_change_token_current", "default_value": "''::character varying", "data_type": "character varying", "format": "<PERSON><PERSON><PERSON>", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29734, "schema": "auth", "table": "users", "id": "29734.29", "ordinal_position": 29, "name": "email_change_confirm_status", "default_value": "0", "data_type": "smallint", "format": "int2", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": "email_change_confirm_status >= 0 AND email_change_confirm_status <= 2", "enums": [], "comment": null}, {"table_id": 29734, "schema": "auth", "table": "users", "id": "29734.30", "ordinal_position": 30, "name": "banned_until", "default_value": null, "data_type": "timestamp with time zone", "format": "timestamptz", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29734, "schema": "auth", "table": "users", "id": "29734.31", "ordinal_position": 31, "name": "reauthentication_token", "default_value": "''::character varying", "data_type": "character varying", "format": "<PERSON><PERSON><PERSON>", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29734, "schema": "auth", "table": "users", "id": "29734.32", "ordinal_position": 32, "name": "reauthentication_sent_at", "default_value": null, "data_type": "timestamp with time zone", "format": "timestamptz", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29734, "schema": "auth", "table": "users", "id": "29734.33", "ordinal_position": 33, "name": "is_sso_user", "default_value": "false", "data_type": "boolean", "format": "bool", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": "Auth: Set this column to true when the account comes from SSO. These accounts can have duplicate emails."}, {"table_id": 29734, "schema": "auth", "table": "users", "id": "29734.34", "ordinal_position": 34, "name": "deleted_at", "default_value": null, "data_type": "timestamp with time zone", "format": "timestamptz", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29734, "schema": "auth", "table": "users", "id": "29734.35", "ordinal_position": 35, "name": "is_anonymous", "default_value": "false", "data_type": "boolean", "format": "bool", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}]}, {"id": 29749, "schema": "realtime", "name": "messages", "rls_enabled": true, "rls_forced": false, "replica_identity": "DEFAULT", "bytes": 0, "size": "0 bytes", "live_rows_estimate": 0, "dead_rows_estimate": 0, "comment": null, "primary_keys": [{"name": "inserted_at", "schema": "realtime", "table_id": 29749, "table_name": "messages"}, {"name": "id", "schema": "realtime", "table_id": 29749, "table_name": "messages"}], "relationships": [], "columns": [{"table_id": 29749, "schema": "realtime", "table": "messages", "id": "29749.1", "ordinal_position": 1, "name": "topic", "default_value": null, "data_type": "text", "format": "text", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29749, "schema": "realtime", "table": "messages", "id": "29749.2", "ordinal_position": 2, "name": "extension", "default_value": null, "data_type": "text", "format": "text", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29749, "schema": "realtime", "table": "messages", "id": "29749.3", "ordinal_position": 3, "name": "payload", "default_value": null, "data_type": "jsonb", "format": "jsonb", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29749, "schema": "realtime", "table": "messages", "id": "29749.4", "ordinal_position": 4, "name": "event", "default_value": null, "data_type": "text", "format": "text", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29749, "schema": "realtime", "table": "messages", "id": "29749.5", "ordinal_position": 5, "name": "private", "default_value": "false", "data_type": "boolean", "format": "bool", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29749, "schema": "realtime", "table": "messages", "id": "29749.6", "ordinal_position": 6, "name": "updated_at", "default_value": "now()", "data_type": "timestamp without time zone", "format": "timestamp", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29749, "schema": "realtime", "table": "messages", "id": "29749.7", "ordinal_position": 7, "name": "inserted_at", "default_value": "now()", "data_type": "timestamp without time zone", "format": "timestamp", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29749, "schema": "realtime", "table": "messages", "id": "29749.8", "ordinal_position": 8, "name": "id", "default_value": "gen_random_uuid()", "data_type": "uuid", "format": "uuid", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}]}, {"id": 29756, "schema": "realtime", "name": "schema_migrations", "rls_enabled": false, "rls_forced": false, "replica_identity": "DEFAULT", "bytes": 24576, "size": "24 kB", "live_rows_estimate": 60, "dead_rows_estimate": 0, "comment": null, "primary_keys": [{"name": "version", "schema": "realtime", "table_id": 29756, "table_name": "schema_migrations"}], "relationships": [], "columns": [{"table_id": 29756, "schema": "realtime", "table": "schema_migrations", "id": "29756.1", "ordinal_position": 1, "name": "version", "default_value": null, "data_type": "bigint", "format": "int8", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29756, "schema": "realtime", "table": "schema_migrations", "id": "29756.2", "ordinal_position": 2, "name": "inserted_at", "default_value": null, "data_type": "timestamp without time zone", "format": "timestamp", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}]}, {"id": 29759, "schema": "realtime", "name": "subscription", "rls_enabled": false, "rls_forced": false, "replica_identity": "DEFAULT", "bytes": 32768, "size": "32 kB", "live_rows_estimate": 0, "dead_rows_estimate": 0, "comment": null, "primary_keys": [{"name": "id", "schema": "realtime", "table_id": 29759, "table_name": "subscription"}], "relationships": [], "columns": [{"table_id": 29759, "schema": "realtime", "table": "subscription", "id": "29759.1", "ordinal_position": 1, "name": "id", "default_value": null, "data_type": "bigint", "format": "int8", "is_identity": true, "identity_generation": "ALWAYS", "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29759, "schema": "realtime", "table": "subscription", "id": "29759.2", "ordinal_position": 2, "name": "subscription_id", "default_value": null, "data_type": "uuid", "format": "uuid", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29759, "schema": "realtime", "table": "subscription", "id": "29759.3", "ordinal_position": 3, "name": "entity", "default_value": null, "data_type": "regclass", "format": "regclass", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29759, "schema": "realtime", "table": "subscription", "id": "29759.4", "ordinal_position": 4, "name": "filters", "default_value": "'{}'::realtime.user_defined_filter[]", "data_type": "ARRAY", "format": "_user_defined_filter", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29759, "schema": "realtime", "table": "subscription", "id": "29759.5", "ordinal_position": 5, "name": "claims", "default_value": null, "data_type": "jsonb", "format": "jsonb", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29759, "schema": "realtime", "table": "subscription", "id": "29759.6", "ordinal_position": 6, "name": "claims_role", "default_value": "realtime.to_regrole((claims ->> 'role'::text))", "data_type": "regrole", "format": "regrole", "is_identity": false, "identity_generation": null, "is_generated": true, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29759, "schema": "realtime", "table": "subscription", "id": "29759.7", "ordinal_position": 7, "name": "created_at", "default_value": "timezone('utc'::text, now())", "data_type": "timestamp without time zone", "format": "timestamp", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}]}, {"id": 29768, "schema": "storage", "name": "buckets", "rls_enabled": true, "rls_forced": false, "replica_identity": "DEFAULT", "bytes": 24576, "size": "24 kB", "live_rows_estimate": 0, "dead_rows_estimate": 0, "comment": null, "primary_keys": [{"name": "id", "schema": "storage", "table_id": 29768, "table_name": "buckets"}], "relationships": [{"id": 29981, "source_schema": "storage", "constraint_name": "s3_multipart_uploads_parts_bucket_id_fkey", "source_table_name": "s3_multipart_uploads_parts", "target_table_name": "buckets", "source_column_name": "bucket_id", "target_column_name": "id", "target_table_schema": "storage"}, {"id": 29971, "source_schema": "storage", "constraint_name": "objects_bucketId_fkey", "source_table_name": "objects", "target_table_name": "buckets", "source_column_name": "bucket_id", "target_column_name": "id", "target_table_schema": "storage"}, {"id": 29976, "source_schema": "storage", "constraint_name": "s3_multipart_uploads_bucket_id_fkey", "source_table_name": "s3_multipart_uploads", "target_table_name": "buckets", "source_column_name": "bucket_id", "target_column_name": "id", "target_table_schema": "storage"}], "columns": [{"table_id": 29768, "schema": "storage", "table": "buckets", "id": "29768.1", "ordinal_position": 1, "name": "id", "default_value": null, "data_type": "text", "format": "text", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29768, "schema": "storage", "table": "buckets", "id": "29768.2", "ordinal_position": 2, "name": "name", "default_value": null, "data_type": "text", "format": "text", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29768, "schema": "storage", "table": "buckets", "id": "29768.3", "ordinal_position": 3, "name": "owner", "default_value": null, "data_type": "uuid", "format": "uuid", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": "Field is deprecated, use owner_id instead"}, {"table_id": 29768, "schema": "storage", "table": "buckets", "id": "29768.4", "ordinal_position": 4, "name": "created_at", "default_value": "now()", "data_type": "timestamp with time zone", "format": "timestamptz", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29768, "schema": "storage", "table": "buckets", "id": "29768.5", "ordinal_position": 5, "name": "updated_at", "default_value": "now()", "data_type": "timestamp with time zone", "format": "timestamptz", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29768, "schema": "storage", "table": "buckets", "id": "29768.6", "ordinal_position": 6, "name": "public", "default_value": "false", "data_type": "boolean", "format": "bool", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29768, "schema": "storage", "table": "buckets", "id": "29768.7", "ordinal_position": 7, "name": "avif_autodetection", "default_value": "false", "data_type": "boolean", "format": "bool", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29768, "schema": "storage", "table": "buckets", "id": "29768.8", "ordinal_position": 8, "name": "file_size_limit", "default_value": null, "data_type": "bigint", "format": "int8", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29768, "schema": "storage", "table": "buckets", "id": "29768.9", "ordinal_position": 9, "name": "allowed_mime_types", "default_value": null, "data_type": "ARRAY", "format": "_text", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29768, "schema": "storage", "table": "buckets", "id": "29768.10", "ordinal_position": 10, "name": "owner_id", "default_value": null, "data_type": "text", "format": "text", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}]}, {"id": 29777, "schema": "storage", "name": "migrations", "rls_enabled": true, "rls_forced": false, "replica_identity": "DEFAULT", "bytes": 40960, "size": "40 kB", "live_rows_estimate": 26, "dead_rows_estimate": 0, "comment": null, "primary_keys": [{"name": "id", "schema": "storage", "table_id": 29777, "table_name": "migrations"}], "relationships": [], "columns": [{"table_id": 29777, "schema": "storage", "table": "migrations", "id": "29777.1", "ordinal_position": 1, "name": "id", "default_value": null, "data_type": "integer", "format": "int4", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29777, "schema": "storage", "table": "migrations", "id": "29777.2", "ordinal_position": 2, "name": "name", "default_value": null, "data_type": "character varying", "format": "<PERSON><PERSON><PERSON>", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": true, "check": null, "enums": [], "comment": null}, {"table_id": 29777, "schema": "storage", "table": "migrations", "id": "29777.3", "ordinal_position": 3, "name": "hash", "default_value": null, "data_type": "character varying", "format": "<PERSON><PERSON><PERSON>", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29777, "schema": "storage", "table": "migrations", "id": "29777.4", "ordinal_position": 4, "name": "executed_at", "default_value": "CURRENT_TIMESTAMP", "data_type": "timestamp without time zone", "format": "timestamp", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}]}, {"id": 29781, "schema": "storage", "name": "objects", "rls_enabled": true, "rls_forced": false, "replica_identity": "DEFAULT", "bytes": 40960, "size": "40 kB", "live_rows_estimate": 0, "dead_rows_estimate": 0, "comment": null, "primary_keys": [{"name": "id", "schema": "storage", "table_id": 29781, "table_name": "objects"}], "relationships": [{"id": 29971, "source_schema": "storage", "constraint_name": "objects_bucketId_fkey", "source_table_name": "objects", "target_table_name": "buckets", "source_column_name": "bucket_id", "target_column_name": "id", "target_table_schema": "storage"}], "columns": [{"table_id": 29781, "schema": "storage", "table": "objects", "id": "29781.1", "ordinal_position": 1, "name": "id", "default_value": "gen_random_uuid()", "data_type": "uuid", "format": "uuid", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29781, "schema": "storage", "table": "objects", "id": "29781.2", "ordinal_position": 2, "name": "bucket_id", "default_value": null, "data_type": "text", "format": "text", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29781, "schema": "storage", "table": "objects", "id": "29781.3", "ordinal_position": 3, "name": "name", "default_value": null, "data_type": "text", "format": "text", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29781, "schema": "storage", "table": "objects", "id": "29781.4", "ordinal_position": 4, "name": "owner", "default_value": null, "data_type": "uuid", "format": "uuid", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": "Field is deprecated, use owner_id instead"}, {"table_id": 29781, "schema": "storage", "table": "objects", "id": "29781.5", "ordinal_position": 5, "name": "created_at", "default_value": "now()", "data_type": "timestamp with time zone", "format": "timestamptz", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29781, "schema": "storage", "table": "objects", "id": "29781.6", "ordinal_position": 6, "name": "updated_at", "default_value": "now()", "data_type": "timestamp with time zone", "format": "timestamptz", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29781, "schema": "storage", "table": "objects", "id": "29781.7", "ordinal_position": 7, "name": "last_accessed_at", "default_value": "now()", "data_type": "timestamp with time zone", "format": "timestamptz", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29781, "schema": "storage", "table": "objects", "id": "29781.8", "ordinal_position": 8, "name": "metadata", "default_value": null, "data_type": "jsonb", "format": "jsonb", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29781, "schema": "storage", "table": "objects", "id": "29781.9", "ordinal_position": 9, "name": "path_tokens", "default_value": "string_to_array(name, '/'::text)", "data_type": "ARRAY", "format": "_text", "is_identity": false, "identity_generation": null, "is_generated": true, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29781, "schema": "storage", "table": "objects", "id": "29781.10", "ordinal_position": 10, "name": "version", "default_value": null, "data_type": "text", "format": "text", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29781, "schema": "storage", "table": "objects", "id": "29781.11", "ordinal_position": 11, "name": "owner_id", "default_value": null, "data_type": "text", "format": "text", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29781, "schema": "storage", "table": "objects", "id": "29781.12", "ordinal_position": 12, "name": "user_metadata", "default_value": null, "data_type": "jsonb", "format": "jsonb", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}]}, {"id": 29791, "schema": "storage", "name": "s3_multipart_uploads", "rls_enabled": true, "rls_forced": false, "replica_identity": "DEFAULT", "bytes": 24576, "size": "24 kB", "live_rows_estimate": 0, "dead_rows_estimate": 0, "comment": null, "primary_keys": [{"name": "id", "schema": "storage", "table_id": 29791, "table_name": "s3_multipart_uploads"}], "relationships": [{"id": 29986, "source_schema": "storage", "constraint_name": "s3_multipart_uploads_parts_upload_id_fkey", "source_table_name": "s3_multipart_uploads_parts", "target_table_name": "s3_multipart_uploads", "source_column_name": "upload_id", "target_column_name": "id", "target_table_schema": "storage"}, {"id": 29976, "source_schema": "storage", "constraint_name": "s3_multipart_uploads_bucket_id_fkey", "source_table_name": "s3_multipart_uploads", "target_table_name": "buckets", "source_column_name": "bucket_id", "target_column_name": "id", "target_table_schema": "storage"}], "columns": [{"table_id": 29791, "schema": "storage", "table": "s3_multipart_uploads", "id": "29791.1", "ordinal_position": 1, "name": "id", "default_value": null, "data_type": "text", "format": "text", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29791, "schema": "storage", "table": "s3_multipart_uploads", "id": "29791.2", "ordinal_position": 2, "name": "in_progress_size", "default_value": "0", "data_type": "bigint", "format": "int8", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29791, "schema": "storage", "table": "s3_multipart_uploads", "id": "29791.3", "ordinal_position": 3, "name": "upload_signature", "default_value": null, "data_type": "text", "format": "text", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29791, "schema": "storage", "table": "s3_multipart_uploads", "id": "29791.4", "ordinal_position": 4, "name": "bucket_id", "default_value": null, "data_type": "text", "format": "text", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29791, "schema": "storage", "table": "s3_multipart_uploads", "id": "29791.5", "ordinal_position": 5, "name": "key", "default_value": null, "data_type": "text", "format": "text", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29791, "schema": "storage", "table": "s3_multipart_uploads", "id": "29791.6", "ordinal_position": 6, "name": "version", "default_value": null, "data_type": "text", "format": "text", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29791, "schema": "storage", "table": "s3_multipart_uploads", "id": "29791.7", "ordinal_position": 7, "name": "owner_id", "default_value": null, "data_type": "text", "format": "text", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29791, "schema": "storage", "table": "s3_multipart_uploads", "id": "29791.8", "ordinal_position": 8, "name": "created_at", "default_value": "now()", "data_type": "timestamp with time zone", "format": "timestamptz", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29791, "schema": "storage", "table": "s3_multipart_uploads", "id": "29791.9", "ordinal_position": 9, "name": "user_metadata", "default_value": null, "data_type": "jsonb", "format": "jsonb", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}]}, {"id": 29798, "schema": "storage", "name": "s3_multipart_uploads_parts", "rls_enabled": true, "rls_forced": false, "replica_identity": "DEFAULT", "bytes": 16384, "size": "16 kB", "live_rows_estimate": 0, "dead_rows_estimate": 0, "comment": null, "primary_keys": [{"name": "id", "schema": "storage", "table_id": 29798, "table_name": "s3_multipart_uploads_parts"}], "relationships": [{"id": 29986, "source_schema": "storage", "constraint_name": "s3_multipart_uploads_parts_upload_id_fkey", "source_table_name": "s3_multipart_uploads_parts", "target_table_name": "s3_multipart_uploads", "source_column_name": "upload_id", "target_column_name": "id", "target_table_schema": "storage"}, {"id": 29981, "source_schema": "storage", "constraint_name": "s3_multipart_uploads_parts_bucket_id_fkey", "source_table_name": "s3_multipart_uploads_parts", "target_table_name": "buckets", "source_column_name": "bucket_id", "target_column_name": "id", "target_table_schema": "storage"}], "columns": [{"table_id": 29798, "schema": "storage", "table": "s3_multipart_uploads_parts", "id": "29798.1", "ordinal_position": 1, "name": "id", "default_value": "gen_random_uuid()", "data_type": "uuid", "format": "uuid", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29798, "schema": "storage", "table": "s3_multipart_uploads_parts", "id": "29798.2", "ordinal_position": 2, "name": "upload_id", "default_value": null, "data_type": "text", "format": "text", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29798, "schema": "storage", "table": "s3_multipart_uploads_parts", "id": "29798.3", "ordinal_position": 3, "name": "size", "default_value": "0", "data_type": "bigint", "format": "int8", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29798, "schema": "storage", "table": "s3_multipart_uploads_parts", "id": "29798.4", "ordinal_position": 4, "name": "part_number", "default_value": null, "data_type": "integer", "format": "int4", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29798, "schema": "storage", "table": "s3_multipart_uploads_parts", "id": "29798.5", "ordinal_position": 5, "name": "bucket_id", "default_value": null, "data_type": "text", "format": "text", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29798, "schema": "storage", "table": "s3_multipart_uploads_parts", "id": "29798.6", "ordinal_position": 6, "name": "key", "default_value": null, "data_type": "text", "format": "text", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29798, "schema": "storage", "table": "s3_multipart_uploads_parts", "id": "29798.7", "ordinal_position": 7, "name": "etag", "default_value": null, "data_type": "text", "format": "text", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29798, "schema": "storage", "table": "s3_multipart_uploads_parts", "id": "29798.8", "ordinal_position": 8, "name": "owner_id", "default_value": null, "data_type": "text", "format": "text", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29798, "schema": "storage", "table": "s3_multipart_uploads_parts", "id": "29798.9", "ordinal_position": 9, "name": "version", "default_value": null, "data_type": "text", "format": "text", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 29798, "schema": "storage", "table": "s3_multipart_uploads_parts", "id": "29798.10", "ordinal_position": 10, "name": "created_at", "default_value": "now()", "data_type": "timestamp with time zone", "format": "timestamptz", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}]}, {"id": 35620, "schema": "public", "name": "option chain", "rls_enabled": true, "rls_forced": false, "replica_identity": "DEFAULT", "bytes": 0, "size": "0 bytes", "live_rows_estimate": 0, "dead_rows_estimate": 0, "comment": "option chain data", "primary_keys": [], "relationships": [], "columns": []}, {"id": 65459, "schema": "public", "name": "nifty", "rls_enabled": true, "rls_forced": false, "replica_identity": "DEFAULT", "bytes": 208928768, "size": "199 MB", "live_rows_estimate": 814034, "dead_rows_estimate": 0, "comment": "Stores complete Nifty options chain data from DhanHQ API", "primary_keys": [{"name": "id", "schema": "public", "table_id": 65459, "table_name": "nifty"}], "relationships": [], "columns": [{"table_id": 65459, "schema": "public", "table": "nifty", "id": "65459.1", "ordinal_position": 1, "name": "id", "default_value": null, "data_type": "bigint", "format": "int8", "is_identity": true, "identity_generation": "BY DEFAULT", "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 65459, "schema": "public", "table": "nifty", "id": "65459.2", "ordinal_position": 2, "name": "timestamp", "default_value": null, "data_type": "timestamp with time zone", "format": "timestamptz", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 65459, "schema": "public", "table": "nifty", "id": "65459.3", "ordinal_position": 3, "name": "expiry_date", "default_value": null, "data_type": "text", "format": "text", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 65459, "schema": "public", "table": "nifty", "id": "65459.4", "ordinal_position": 4, "name": "strike_price", "default_value": null, "data_type": "numeric", "format": "numeric", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 65459, "schema": "public", "table": "nifty", "id": "65459.5", "ordinal_position": 5, "name": "option_type", "default_value": null, "data_type": "text", "format": "text", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": "option_type = ANY (ARRAY['CE'::text, 'PE'::text])", "enums": [], "comment": null}, {"table_id": 65459, "schema": "public", "table": "nifty", "id": "65459.6", "ordinal_position": 6, "name": "spot_price", "default_value": null, "data_type": "numeric", "format": "numeric", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 65459, "schema": "public", "table": "nifty", "id": "65459.7", "ordinal_position": 7, "name": "last_price", "default_value": null, "data_type": "numeric", "format": "numeric", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 65459, "schema": "public", "table": "nifty", "id": "65459.8", "ordinal_position": 8, "name": "previous_close_price", "default_value": null, "data_type": "numeric", "format": "numeric", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 65459, "schema": "public", "table": "nifty", "id": "65459.9", "ordinal_position": 9, "name": "top_bid_price", "default_value": null, "data_type": "numeric", "format": "numeric", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 65459, "schema": "public", "table": "nifty", "id": "65459.10", "ordinal_position": 10, "name": "top_ask_price", "default_value": null, "data_type": "numeric", "format": "numeric", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 65459, "schema": "public", "table": "nifty", "id": "65459.11", "ordinal_position": 11, "name": "volume", "default_value": null, "data_type": "integer", "format": "int4", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 65459, "schema": "public", "table": "nifty", "id": "65459.12", "ordinal_position": 12, "name": "previous_volume", "default_value": null, "data_type": "integer", "format": "int4", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 65459, "schema": "public", "table": "nifty", "id": "65459.13", "ordinal_position": 13, "name": "oi", "default_value": null, "data_type": "integer", "format": "int4", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 65459, "schema": "public", "table": "nifty", "id": "65459.14", "ordinal_position": 14, "name": "previous_oi", "default_value": null, "data_type": "integer", "format": "int4", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 65459, "schema": "public", "table": "nifty", "id": "65459.15", "ordinal_position": 15, "name": "top_bid_quantity", "default_value": null, "data_type": "integer", "format": "int4", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 65459, "schema": "public", "table": "nifty", "id": "65459.16", "ordinal_position": 16, "name": "top_ask_quantity", "default_value": null, "data_type": "integer", "format": "int4", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 65459, "schema": "public", "table": "nifty", "id": "65459.17", "ordinal_position": 17, "name": "iv", "default_value": null, "data_type": "numeric", "format": "numeric", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 65459, "schema": "public", "table": "nifty", "id": "65459.18", "ordinal_position": 18, "name": "delta", "default_value": null, "data_type": "numeric", "format": "numeric", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 65459, "schema": "public", "table": "nifty", "id": "65459.19", "ordinal_position": 19, "name": "gamma", "default_value": null, "data_type": "numeric", "format": "numeric", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 65459, "schema": "public", "table": "nifty", "id": "65459.20", "ordinal_position": 20, "name": "theta", "default_value": null, "data_type": "numeric", "format": "numeric", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 65459, "schema": "public", "table": "nifty", "id": "65459.21", "ordinal_position": 21, "name": "vega", "default_value": null, "data_type": "numeric", "format": "numeric", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 65459, "schema": "public", "table": "nifty", "id": "65459.22", "ordinal_position": 22, "name": "created_at", "default_value": "timezone('utc'::text, now())", "data_type": "timestamp with time zone", "format": "timestamptz", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}]}, {"id": 67762, "schema": "_timescaledb_catalog", "name": "hypertable", "rls_enabled": false, "rls_forced": false, "replica_identity": "DEFAULT", "bytes": 57344, "size": "56 kB", "live_rows_estimate": 1, "dead_rows_estimate": 2, "comment": null, "primary_keys": [{"name": "id", "schema": "_timescaledb_catalog", "table_id": 67762, "table_name": "hypertable"}], "relationships": [{"id": 67811, "source_schema": "_timescaledb_catalog", "constraint_name": "dimension_hypertable_id_fkey", "source_table_name": "dimension", "target_table_name": "hypertable", "source_column_name": "hypertable_id", "target_column_name": "id", "target_table_schema": "_timescaledb_catalog"}, {"id": 67996, "source_schema": "_timescaledb_catalog", "constraint_name": "continuous_agg_raw_hypertable_id_fkey", "source_table_name": "continuous_agg", "target_table_name": "hypertable", "source_column_name": "raw_hypertable_id", "target_column_name": "id", "target_table_schema": "_timescaledb_catalog"}, {"id": 67778, "source_schema": "_timescaledb_catalog", "constraint_name": "hypertable_compressed_hypertable_id_fkey", "source_table_name": "hypertable", "target_table_name": "hypertable", "source_column_name": "compressed_hypertable_id", "target_column_name": "id", "target_table_schema": "_timescaledb_catalog"}, {"id": 67792, "source_schema": "_timescaledb_catalog", "constraint_name": "tablespace_hypertable_id_fkey", "source_table_name": "tablespace", "target_table_name": "hypertable", "source_column_name": "hypertable_id", "target_column_name": "id", "target_table_schema": "_timescaledb_catalog"}, {"id": 67900, "source_schema": "_timescaledb_catalog", "constraint_name": "chunk_column_stats_hypertable_id_fkey", "source_table_name": "chunk_column_stats", "target_table_name": "hypertable", "source_column_name": "hypertable_id", "target_column_name": "id", "target_table_schema": "_timescaledb_catalog"}, {"id": 67991, "source_schema": "_timescaledb_catalog", "constraint_name": "continuous_agg_mat_hypertable_id_fkey", "source_table_name": "continuous_agg", "target_table_name": "hypertable", "source_column_name": "mat_hypertable_id", "target_column_name": "id", "target_table_schema": "_timescaledb_catalog"}, {"id": 67848, "source_schema": "_timescaledb_catalog", "constraint_name": "chunk_hypertable_id_fkey", "source_table_name": "chunk", "target_table_name": "hypertable", "source_column_name": "hypertable_id", "target_column_name": "id", "target_table_schema": "_timescaledb_catalog"}, {"id": 68025, "source_schema": "_timescaledb_catalog", "constraint_name": "continuous_aggs_invalidation_threshold_hypertable_id_fkey", "source_table_name": "continuous_aggs_invalidation_threshold", "target_table_name": "hypertable", "source_column_name": "hypertable_id", "target_column_name": "id", "target_table_schema": "_timescaledb_catalog"}, {"id": 68015, "source_schema": "_timescaledb_catalog", "constraint_name": "continuous_aggs_bucket_function_mat_hypertable_id_fkey", "source_table_name": "continuous_aggs_bucket_function", "target_table_name": "hypertable", "source_column_name": "mat_hypertable_id", "target_column_name": "id", "target_table_schema": "_timescaledb_catalog"}, {"id": 67884, "source_schema": "_timescaledb_catalog", "constraint_name": "chunk_index_hypertable_id_fkey", "source_table_name": "chunk_index", "target_table_name": "hypertable", "source_column_name": "hypertable_id", "target_column_name": "id", "target_table_schema": "_timescaledb_catalog"}, {"id": 67922, "source_schema": "_timescaledb_config", "constraint_name": "bgw_job_hypertable_id_fkey", "source_table_name": "bgw_job", "target_table_name": "hypertable", "source_column_name": "hypertable_id", "target_column_name": "id", "target_table_schema": "_timescaledb_catalog"}], "columns": [{"table_id": 67762, "schema": "_timescaledb_catalog", "table": "hypertable", "id": "67762.1", "ordinal_position": 1, "name": "id", "default_value": "nextval('_timescaledb_catalog.hypertable_id_seq'::regclass)", "data_type": "integer", "format": "int4", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67762, "schema": "_timescaledb_catalog", "table": "hypertable", "id": "67762.2", "ordinal_position": 2, "name": "schema_name", "default_value": null, "data_type": "name", "format": "name", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": "schema_name <> '_timescaledb_catalog'::name", "enums": [], "comment": null}, {"table_id": 67762, "schema": "_timescaledb_catalog", "table": "hypertable", "id": "67762.3", "ordinal_position": 3, "name": "table_name", "default_value": null, "data_type": "name", "format": "name", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67762, "schema": "_timescaledb_catalog", "table": "hypertable", "id": "67762.4", "ordinal_position": 4, "name": "associated_schema_name", "default_value": null, "data_type": "name", "format": "name", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67762, "schema": "_timescaledb_catalog", "table": "hypertable", "id": "67762.5", "ordinal_position": 5, "name": "associated_table_prefix", "default_value": null, "data_type": "name", "format": "name", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67762, "schema": "_timescaledb_catalog", "table": "hypertable", "id": "67762.6", "ordinal_position": 6, "name": "num_dimensions", "default_value": null, "data_type": "smallint", "format": "int2", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67762, "schema": "_timescaledb_catalog", "table": "hypertable", "id": "67762.7", "ordinal_position": 7, "name": "chunk_sizing_func_schema", "default_value": null, "data_type": "name", "format": "name", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67762, "schema": "_timescaledb_catalog", "table": "hypertable", "id": "67762.8", "ordinal_position": 8, "name": "chunk_sizing_func_name", "default_value": null, "data_type": "name", "format": "name", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67762, "schema": "_timescaledb_catalog", "table": "hypertable", "id": "67762.9", "ordinal_position": 9, "name": "chunk_target_size", "default_value": null, "data_type": "bigint", "format": "int8", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": "chunk_target_size >= 0", "enums": [], "comment": null}, {"table_id": 67762, "schema": "_timescaledb_catalog", "table": "hypertable", "id": "67762.10", "ordinal_position": 10, "name": "compression_state", "default_value": "0", "data_type": "smallint", "format": "int2", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67762, "schema": "_timescaledb_catalog", "table": "hypertable", "id": "67762.11", "ordinal_position": 11, "name": "compressed_hypertable_id", "default_value": null, "data_type": "integer", "format": "int4", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67762, "schema": "_timescaledb_catalog", "table": "hypertable", "id": "67762.12", "ordinal_position": 12, "name": "status", "default_value": "0", "data_type": "integer", "format": "int4", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}]}, {"id": 67784, "schema": "_timescaledb_catalog", "name": "tablespace", "rls_enabled": false, "rls_forced": false, "replica_identity": "DEFAULT", "bytes": 16384, "size": "16 kB", "live_rows_estimate": 0, "dead_rows_estimate": 0, "comment": null, "primary_keys": [{"name": "id", "schema": "_timescaledb_catalog", "table_id": 67784, "table_name": "tablespace"}], "relationships": [{"id": 67792, "source_schema": "_timescaledb_catalog", "constraint_name": "tablespace_hypertable_id_fkey", "source_table_name": "tablespace", "target_table_name": "hypertable", "source_column_name": "hypertable_id", "target_column_name": "id", "target_table_schema": "_timescaledb_catalog"}], "columns": [{"table_id": 67784, "schema": "_timescaledb_catalog", "table": "tablespace", "id": "67784.1", "ordinal_position": 1, "name": "id", "default_value": "nextval('_timescaledb_catalog.tablespace_id_seq'::regclass)", "data_type": "integer", "format": "int4", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67784, "schema": "_timescaledb_catalog", "table": "tablespace", "id": "67784.2", "ordinal_position": 2, "name": "hypertable_id", "default_value": null, "data_type": "integer", "format": "int4", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67784, "schema": "_timescaledb_catalog", "table": "tablespace", "id": "67784.3", "ordinal_position": 3, "name": "tablespace_name", "default_value": null, "data_type": "name", "format": "name", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}]}, {"id": 67798, "schema": "_timescaledb_catalog", "name": "dimension", "rls_enabled": false, "rls_forced": false, "replica_identity": "DEFAULT", "bytes": 40960, "size": "40 kB", "live_rows_estimate": 1, "dead_rows_estimate": 2, "comment": null, "primary_keys": [{"name": "id", "schema": "_timescaledb_catalog", "table_id": 67798, "table_name": "dimension"}], "relationships": [{"id": 67826, "source_schema": "_timescaledb_catalog", "constraint_name": "dimension_slice_dimension_id_fkey", "source_table_name": "dimension_slice", "target_table_name": "dimension", "source_column_name": "dimension_id", "target_column_name": "id", "target_table_schema": "_timescaledb_catalog"}, {"id": 67811, "source_schema": "_timescaledb_catalog", "constraint_name": "dimension_hypertable_id_fkey", "source_table_name": "dimension", "target_table_name": "hypertable", "source_column_name": "hypertable_id", "target_column_name": "id", "target_table_schema": "_timescaledb_catalog"}], "columns": [{"table_id": 67798, "schema": "_timescaledb_catalog", "table": "dimension", "id": "67798.1", "ordinal_position": 1, "name": "id", "default_value": "nextval('_timescaledb_catalog.dimension_id_seq'::regclass)", "data_type": "integer", "format": "int4", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67798, "schema": "_timescaledb_catalog", "table": "dimension", "id": "67798.2", "ordinal_position": 2, "name": "hypertable_id", "default_value": null, "data_type": "integer", "format": "int4", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67798, "schema": "_timescaledb_catalog", "table": "dimension", "id": "67798.3", "ordinal_position": 3, "name": "column_name", "default_value": null, "data_type": "name", "format": "name", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67798, "schema": "_timescaledb_catalog", "table": "dimension", "id": "67798.4", "ordinal_position": 4, "name": "column_type", "default_value": null, "data_type": "regtype", "format": "regtype", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67798, "schema": "_timescaledb_catalog", "table": "dimension", "id": "67798.5", "ordinal_position": 5, "name": "aligned", "default_value": null, "data_type": "boolean", "format": "bool", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67798, "schema": "_timescaledb_catalog", "table": "dimension", "id": "67798.6", "ordinal_position": 6, "name": "num_slices", "default_value": null, "data_type": "smallint", "format": "int2", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67798, "schema": "_timescaledb_catalog", "table": "dimension", "id": "67798.7", "ordinal_position": 7, "name": "partitioning_func_schema", "default_value": null, "data_type": "name", "format": "name", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67798, "schema": "_timescaledb_catalog", "table": "dimension", "id": "67798.8", "ordinal_position": 8, "name": "partitioning_func", "default_value": null, "data_type": "name", "format": "name", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67798, "schema": "_timescaledb_catalog", "table": "dimension", "id": "67798.9", "ordinal_position": 9, "name": "interval_length", "default_value": null, "data_type": "bigint", "format": "int8", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": "interval_length IS NULL OR interval_length > 0", "enums": [], "comment": null}, {"table_id": 67798, "schema": "_timescaledb_catalog", "table": "dimension", "id": "67798.10", "ordinal_position": 10, "name": "compress_interval_length", "default_value": null, "data_type": "bigint", "format": "int8", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": "compress_interval_length IS NULL OR compress_interval_length > 0", "enums": [], "comment": null}, {"table_id": 67798, "schema": "_timescaledb_catalog", "table": "dimension", "id": "67798.11", "ordinal_position": 11, "name": "integer_now_func_schema", "default_value": null, "data_type": "name", "format": "name", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67798, "schema": "_timescaledb_catalog", "table": "dimension", "id": "67798.12", "ordinal_position": 12, "name": "integer_now_func", "default_value": null, "data_type": "name", "format": "name", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}]}, {"id": 67817, "schema": "_timescaledb_catalog", "name": "dimension_slice", "rls_enabled": false, "rls_forced": false, "replica_identity": "DEFAULT", "bytes": 16384, "size": "16 kB", "live_rows_estimate": 0, "dead_rows_estimate": 0, "comment": null, "primary_keys": [{"name": "id", "schema": "_timescaledb_catalog", "table_id": 67817, "table_name": "dimension_slice"}], "relationships": [{"id": 67867, "source_schema": "_timescaledb_catalog", "constraint_name": "chunk_constraint_dimension_slice_id_fkey", "source_table_name": "chunk_constraint", "target_table_name": "dimension_slice", "source_column_name": "dimension_slice_id", "target_column_name": "id", "target_table_schema": "_timescaledb_catalog"}, {"id": 67826, "source_schema": "_timescaledb_catalog", "constraint_name": "dimension_slice_dimension_id_fkey", "source_table_name": "dimension_slice", "target_table_name": "dimension", "source_column_name": "dimension_id", "target_column_name": "id", "target_table_schema": "_timescaledb_catalog"}], "columns": [{"table_id": 67817, "schema": "_timescaledb_catalog", "table": "dimension_slice", "id": "67817.1", "ordinal_position": 1, "name": "id", "default_value": "nextval('_timescaledb_catalog.dimension_slice_id_seq'::regclass)", "data_type": "integer", "format": "int4", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67817, "schema": "_timescaledb_catalog", "table": "dimension_slice", "id": "67817.2", "ordinal_position": 2, "name": "dimension_id", "default_value": null, "data_type": "integer", "format": "int4", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67817, "schema": "_timescaledb_catalog", "table": "dimension_slice", "id": "67817.3", "ordinal_position": 3, "name": "range_start", "default_value": null, "data_type": "bigint", "format": "int8", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67817, "schema": "_timescaledb_catalog", "table": "dimension_slice", "id": "67817.4", "ordinal_position": 4, "name": "range_end", "default_value": null, "data_type": "bigint", "format": "int8", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}]}, {"id": 67832, "schema": "_timescaledb_catalog", "name": "chunk", "rls_enabled": false, "rls_forced": false, "replica_identity": "DEFAULT", "bytes": 49152, "size": "48 kB", "live_rows_estimate": 0, "dead_rows_estimate": 0, "comment": null, "primary_keys": [{"name": "id", "schema": "_timescaledb_catalog", "table_id": 67832, "table_name": "chunk"}], "relationships": [{"id": 67843, "source_schema": "_timescaledb_catalog", "constraint_name": "chunk_compressed_chunk_id_fkey", "source_table_name": "chunk", "target_table_name": "chunk", "source_column_name": "compressed_chunk_id", "target_column_name": "id", "target_table_schema": "_timescaledb_catalog"}, {"id": 67848, "source_schema": "_timescaledb_catalog", "constraint_name": "chunk_hypertable_id_fkey", "source_table_name": "chunk", "target_table_name": "hypertable", "source_column_name": "hypertable_id", "target_column_name": "id", "target_table_schema": "_timescaledb_catalog"}, {"id": 67862, "source_schema": "_timescaledb_catalog", "constraint_name": "chunk_constraint_chunk_id_fkey", "source_table_name": "chunk_constraint", "target_table_name": "chunk", "source_column_name": "chunk_id", "target_column_name": "id", "target_table_schema": "_timescaledb_catalog"}, {"id": 67879, "source_schema": "_timescaledb_catalog", "constraint_name": "chunk_index_chunk_id_fkey", "source_table_name": "chunk_index", "target_table_name": "chunk", "source_column_name": "chunk_id", "target_column_name": "id", "target_table_schema": "_timescaledb_catalog"}, {"id": 67905, "source_schema": "_timescaledb_catalog", "constraint_name": "chunk_column_stats_chunk_id_fkey", "source_table_name": "chunk_column_stats", "target_table_name": "chunk", "source_column_name": "chunk_id", "target_column_name": "id", "target_table_schema": "_timescaledb_catalog"}, {"id": 67957, "source_schema": "_timescaledb_internal", "constraint_name": "bgw_policy_chunk_stats_chunk_id_fkey", "source_table_name": "bgw_policy_chunk_stats", "target_table_name": "chunk", "source_column_name": "chunk_id", "target_column_name": "id", "target_table_schema": "_timescaledb_catalog"}, {"id": 68075, "source_schema": "_timescaledb_catalog", "constraint_name": "compression_chunk_size_chunk_id_fkey", "source_table_name": "compression_chunk_size", "target_table_name": "chunk", "source_column_name": "chunk_id", "target_column_name": "id", "target_table_schema": "_timescaledb_catalog"}, {"id": 68080, "source_schema": "_timescaledb_catalog", "constraint_name": "compression_chunk_size_compressed_chunk_id_fkey", "source_table_name": "compression_chunk_size", "target_table_name": "chunk", "source_column_name": "compressed_chunk_id", "target_column_name": "id", "target_table_schema": "_timescaledb_catalog"}], "columns": [{"table_id": 67832, "schema": "_timescaledb_catalog", "table": "chunk", "id": "67832.1", "ordinal_position": 1, "name": "id", "default_value": "nextval('_timescaledb_catalog.chunk_id_seq'::regclass)", "data_type": "integer", "format": "int4", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67832, "schema": "_timescaledb_catalog", "table": "chunk", "id": "67832.2", "ordinal_position": 2, "name": "hypertable_id", "default_value": null, "data_type": "integer", "format": "int4", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67832, "schema": "_timescaledb_catalog", "table": "chunk", "id": "67832.3", "ordinal_position": 3, "name": "schema_name", "default_value": null, "data_type": "name", "format": "name", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67832, "schema": "_timescaledb_catalog", "table": "chunk", "id": "67832.4", "ordinal_position": 4, "name": "table_name", "default_value": null, "data_type": "name", "format": "name", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67832, "schema": "_timescaledb_catalog", "table": "chunk", "id": "67832.5", "ordinal_position": 5, "name": "compressed_chunk_id", "default_value": null, "data_type": "integer", "format": "int4", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67832, "schema": "_timescaledb_catalog", "table": "chunk", "id": "67832.6", "ordinal_position": 6, "name": "dropped", "default_value": "false", "data_type": "boolean", "format": "bool", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67832, "schema": "_timescaledb_catalog", "table": "chunk", "id": "67832.7", "ordinal_position": 7, "name": "status", "default_value": "0", "data_type": "integer", "format": "int4", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67832, "schema": "_timescaledb_catalog", "table": "chunk", "id": "67832.8", "ordinal_position": 8, "name": "osm_chunk", "default_value": "false", "data_type": "boolean", "format": "bool", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67832, "schema": "_timescaledb_catalog", "table": "chunk", "id": "67832.9", "ordinal_position": 9, "name": "creation_time", "default_value": null, "data_type": "timestamp with time zone", "format": "timestamptz", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}]}, {"id": 67857, "schema": "_timescaledb_catalog", "name": "chunk_constraint", "rls_enabled": false, "rls_forced": false, "replica_identity": "DEFAULT", "bytes": 16384, "size": "16 kB", "live_rows_estimate": 0, "dead_rows_estimate": 0, "comment": null, "primary_keys": [], "relationships": [{"id": 67867, "source_schema": "_timescaledb_catalog", "constraint_name": "chunk_constraint_dimension_slice_id_fkey", "source_table_name": "chunk_constraint", "target_table_name": "dimension_slice", "source_column_name": "dimension_slice_id", "target_column_name": "id", "target_table_schema": "_timescaledb_catalog"}, {"id": 67862, "source_schema": "_timescaledb_catalog", "constraint_name": "chunk_constraint_chunk_id_fkey", "source_table_name": "chunk_constraint", "target_table_name": "chunk", "source_column_name": "chunk_id", "target_column_name": "id", "target_table_schema": "_timescaledb_catalog"}], "columns": [{"table_id": 67857, "schema": "_timescaledb_catalog", "table": "chunk_constraint", "id": "67857.1", "ordinal_position": 1, "name": "chunk_id", "default_value": null, "data_type": "integer", "format": "int4", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67857, "schema": "_timescaledb_catalog", "table": "chunk_constraint", "id": "67857.2", "ordinal_position": 2, "name": "dimension_slice_id", "default_value": null, "data_type": "integer", "format": "int4", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67857, "schema": "_timescaledb_catalog", "table": "chunk_constraint", "id": "67857.3", "ordinal_position": 3, "name": "constraint_name", "default_value": null, "data_type": "name", "format": "name", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67857, "schema": "_timescaledb_catalog", "table": "chunk_constraint", "id": "67857.4", "ordinal_position": 4, "name": "hypertable_constraint_name", "default_value": null, "data_type": "name", "format": "name", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}]}, {"id": 67874, "schema": "_timescaledb_catalog", "name": "chunk_index", "rls_enabled": false, "rls_forced": false, "replica_identity": "DEFAULT", "bytes": 16384, "size": "16 kB", "live_rows_estimate": 0, "dead_rows_estimate": 0, "comment": null, "primary_keys": [], "relationships": [{"id": 67884, "source_schema": "_timescaledb_catalog", "constraint_name": "chunk_index_hypertable_id_fkey", "source_table_name": "chunk_index", "target_table_name": "hypertable", "source_column_name": "hypertable_id", "target_column_name": "id", "target_table_schema": "_timescaledb_catalog"}, {"id": 67879, "source_schema": "_timescaledb_catalog", "constraint_name": "chunk_index_chunk_id_fkey", "source_table_name": "chunk_index", "target_table_name": "chunk", "source_column_name": "chunk_id", "target_column_name": "id", "target_table_schema": "_timescaledb_catalog"}], "columns": [{"table_id": 67874, "schema": "_timescaledb_catalog", "table": "chunk_index", "id": "67874.1", "ordinal_position": 1, "name": "chunk_id", "default_value": null, "data_type": "integer", "format": "int4", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67874, "schema": "_timescaledb_catalog", "table": "chunk_index", "id": "67874.2", "ordinal_position": 2, "name": "index_name", "default_value": null, "data_type": "name", "format": "name", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67874, "schema": "_timescaledb_catalog", "table": "chunk_index", "id": "67874.3", "ordinal_position": 3, "name": "hypertable_id", "default_value": null, "data_type": "integer", "format": "int4", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67874, "schema": "_timescaledb_catalog", "table": "chunk_index", "id": "67874.4", "ordinal_position": 4, "name": "hypertable_index_name", "default_value": null, "data_type": "name", "format": "name", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}]}, {"id": 67891, "schema": "_timescaledb_catalog", "name": "chunk_column_stats", "rls_enabled": false, "rls_forced": false, "replica_identity": "DEFAULT", "bytes": 16384, "size": "16 kB", "live_rows_estimate": 0, "dead_rows_estimate": 0, "comment": null, "primary_keys": [{"name": "id", "schema": "_timescaledb_catalog", "table_id": 67891, "table_name": "chunk_column_stats"}], "relationships": [{"id": 67905, "source_schema": "_timescaledb_catalog", "constraint_name": "chunk_column_stats_chunk_id_fkey", "source_table_name": "chunk_column_stats", "target_table_name": "chunk", "source_column_name": "chunk_id", "target_column_name": "id", "target_table_schema": "_timescaledb_catalog"}, {"id": 67900, "source_schema": "_timescaledb_catalog", "constraint_name": "chunk_column_stats_hypertable_id_fkey", "source_table_name": "chunk_column_stats", "target_table_name": "hypertable", "source_column_name": "hypertable_id", "target_column_name": "id", "target_table_schema": "_timescaledb_catalog"}], "columns": [{"table_id": 67891, "schema": "_timescaledb_catalog", "table": "chunk_column_stats", "id": "67891.1", "ordinal_position": 1, "name": "id", "default_value": "nextval('_timescaledb_catalog.chunk_column_stats_id_seq'::regclass)", "data_type": "integer", "format": "int4", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67891, "schema": "_timescaledb_catalog", "table": "chunk_column_stats", "id": "67891.2", "ordinal_position": 2, "name": "hypertable_id", "default_value": null, "data_type": "integer", "format": "int4", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67891, "schema": "_timescaledb_catalog", "table": "chunk_column_stats", "id": "67891.3", "ordinal_position": 3, "name": "chunk_id", "default_value": null, "data_type": "integer", "format": "int4", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67891, "schema": "_timescaledb_catalog", "table": "chunk_column_stats", "id": "67891.4", "ordinal_position": 4, "name": "column_name", "default_value": null, "data_type": "name", "format": "name", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67891, "schema": "_timescaledb_catalog", "table": "chunk_column_stats", "id": "67891.5", "ordinal_position": 5, "name": "range_start", "default_value": null, "data_type": "bigint", "format": "int8", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67891, "schema": "_timescaledb_catalog", "table": "chunk_column_stats", "id": "67891.6", "ordinal_position": 6, "name": "range_end", "default_value": null, "data_type": "bigint", "format": "int8", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67891, "schema": "_timescaledb_catalog", "table": "chunk_column_stats", "id": "67891.7", "ordinal_position": 7, "name": "valid", "default_value": null, "data_type": "boolean", "format": "bool", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}]}, {"id": 67911, "schema": "_timescaledb_config", "name": "bgw_job", "rls_enabled": false, "rls_forced": false, "replica_identity": "DEFAULT", "bytes": 49152, "size": "48 kB", "live_rows_estimate": 3, "dead_rows_estimate": 0, "comment": null, "primary_keys": [{"name": "id", "schema": "_timescaledb_config", "table_id": 67911, "table_name": "bgw_job"}], "relationships": [{"id": 67935, "source_schema": "_timescaledb_internal", "constraint_name": "bgw_job_stat_job_id_fkey", "source_table_name": "bgw_job_stat", "target_table_name": "bgw_job", "source_column_name": "job_id", "target_column_name": "id", "target_table_schema": "_timescaledb_config"}, {"id": 67962, "source_schema": "_timescaledb_internal", "constraint_name": "bgw_policy_chunk_stats_job_id_fkey", "source_table_name": "bgw_policy_chunk_stats", "target_table_name": "bgw_job", "source_column_name": "job_id", "target_column_name": "id", "target_table_schema": "_timescaledb_config"}, {"id": 67922, "source_schema": "_timescaledb_config", "constraint_name": "bgw_job_hypertable_id_fkey", "source_table_name": "bgw_job", "target_table_name": "hypertable", "source_column_name": "hypertable_id", "target_column_name": "id", "target_table_schema": "_timescaledb_catalog"}], "columns": [{"table_id": 67911, "schema": "_timescaledb_config", "table": "bgw_job", "id": "67911.1", "ordinal_position": 1, "name": "id", "default_value": "nextval('_timescaledb_config.bgw_job_id_seq'::regclass)", "data_type": "integer", "format": "int4", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67911, "schema": "_timescaledb_config", "table": "bgw_job", "id": "67911.2", "ordinal_position": 2, "name": "application_name", "default_value": null, "data_type": "name", "format": "name", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67911, "schema": "_timescaledb_config", "table": "bgw_job", "id": "67911.3", "ordinal_position": 3, "name": "schedule_interval", "default_value": null, "data_type": "interval", "format": "interval", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67911, "schema": "_timescaledb_config", "table": "bgw_job", "id": "67911.4", "ordinal_position": 4, "name": "max_runtime", "default_value": null, "data_type": "interval", "format": "interval", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67911, "schema": "_timescaledb_config", "table": "bgw_job", "id": "67911.5", "ordinal_position": 5, "name": "max_retries", "default_value": null, "data_type": "integer", "format": "int4", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67911, "schema": "_timescaledb_config", "table": "bgw_job", "id": "67911.6", "ordinal_position": 6, "name": "retry_period", "default_value": null, "data_type": "interval", "format": "interval", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67911, "schema": "_timescaledb_config", "table": "bgw_job", "id": "67911.7", "ordinal_position": 7, "name": "proc_schema", "default_value": null, "data_type": "name", "format": "name", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67911, "schema": "_timescaledb_config", "table": "bgw_job", "id": "67911.8", "ordinal_position": 8, "name": "proc_name", "default_value": null, "data_type": "name", "format": "name", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67911, "schema": "_timescaledb_config", "table": "bgw_job", "id": "67911.9", "ordinal_position": 9, "name": "owner", "default_value": "(quote_ident((CURRENT_ROLE)::text))::regrole", "data_type": "regrole", "format": "regrole", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67911, "schema": "_timescaledb_config", "table": "bgw_job", "id": "67911.10", "ordinal_position": 10, "name": "scheduled", "default_value": "true", "data_type": "boolean", "format": "bool", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67911, "schema": "_timescaledb_config", "table": "bgw_job", "id": "67911.11", "ordinal_position": 11, "name": "fixed_schedule", "default_value": "true", "data_type": "boolean", "format": "bool", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67911, "schema": "_timescaledb_config", "table": "bgw_job", "id": "67911.12", "ordinal_position": 12, "name": "initial_start", "default_value": null, "data_type": "timestamp with time zone", "format": "timestamptz", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67911, "schema": "_timescaledb_config", "table": "bgw_job", "id": "67911.13", "ordinal_position": 13, "name": "hypertable_id", "default_value": null, "data_type": "integer", "format": "int4", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67911, "schema": "_timescaledb_config", "table": "bgw_job", "id": "67911.14", "ordinal_position": 14, "name": "config", "default_value": null, "data_type": "jsonb", "format": "jsonb", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67911, "schema": "_timescaledb_config", "table": "bgw_job", "id": "67911.15", "ordinal_position": 15, "name": "check_schema", "default_value": null, "data_type": "name", "format": "name", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67911, "schema": "_timescaledb_config", "table": "bgw_job", "id": "67911.16", "ordinal_position": 16, "name": "check_name", "default_value": null, "data_type": "name", "format": "name", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67911, "schema": "_timescaledb_config", "table": "bgw_job", "id": "67911.17", "ordinal_position": 17, "name": "timezone", "default_value": null, "data_type": "text", "format": "text", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}]}, {"id": 67928, "schema": "_timescaledb_internal", "name": "bgw_job_stat", "rls_enabled": false, "rls_forced": false, "replica_identity": "DEFAULT", "bytes": 24576, "size": "24 kB", "live_rows_estimate": 0, "dead_rows_estimate": 11, "comment": null, "primary_keys": [{"name": "job_id", "schema": "_timescaledb_internal", "table_id": 67928, "table_name": "bgw_job_stat"}], "relationships": [{"id": 67935, "source_schema": "_timescaledb_internal", "constraint_name": "bgw_job_stat_job_id_fkey", "source_table_name": "bgw_job_stat", "target_table_name": "bgw_job", "source_column_name": "job_id", "target_column_name": "id", "target_table_schema": "_timescaledb_config"}], "columns": [{"table_id": 67928, "schema": "_timescaledb_internal", "table": "bgw_job_stat", "id": "67928.1", "ordinal_position": 1, "name": "job_id", "default_value": null, "data_type": "integer", "format": "int4", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67928, "schema": "_timescaledb_internal", "table": "bgw_job_stat", "id": "67928.2", "ordinal_position": 2, "name": "last_start", "default_value": "now()", "data_type": "timestamp with time zone", "format": "timestamptz", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67928, "schema": "_timescaledb_internal", "table": "bgw_job_stat", "id": "67928.3", "ordinal_position": 3, "name": "last_finish", "default_value": null, "data_type": "timestamp with time zone", "format": "timestamptz", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67928, "schema": "_timescaledb_internal", "table": "bgw_job_stat", "id": "67928.4", "ordinal_position": 4, "name": "next_start", "default_value": null, "data_type": "timestamp with time zone", "format": "timestamptz", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67928, "schema": "_timescaledb_internal", "table": "bgw_job_stat", "id": "67928.5", "ordinal_position": 5, "name": "last_successful_finish", "default_value": null, "data_type": "timestamp with time zone", "format": "timestamptz", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67928, "schema": "_timescaledb_internal", "table": "bgw_job_stat", "id": "67928.6", "ordinal_position": 6, "name": "last_run_success", "default_value": null, "data_type": "boolean", "format": "bool", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67928, "schema": "_timescaledb_internal", "table": "bgw_job_stat", "id": "67928.7", "ordinal_position": 7, "name": "total_runs", "default_value": null, "data_type": "bigint", "format": "int8", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67928, "schema": "_timescaledb_internal", "table": "bgw_job_stat", "id": "67928.8", "ordinal_position": 8, "name": "total_duration", "default_value": null, "data_type": "interval", "format": "interval", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67928, "schema": "_timescaledb_internal", "table": "bgw_job_stat", "id": "67928.9", "ordinal_position": 9, "name": "total_duration_failures", "default_value": null, "data_type": "interval", "format": "interval", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67928, "schema": "_timescaledb_internal", "table": "bgw_job_stat", "id": "67928.10", "ordinal_position": 10, "name": "total_successes", "default_value": null, "data_type": "bigint", "format": "int8", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67928, "schema": "_timescaledb_internal", "table": "bgw_job_stat", "id": "67928.11", "ordinal_position": 11, "name": "total_failures", "default_value": null, "data_type": "bigint", "format": "int8", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67928, "schema": "_timescaledb_internal", "table": "bgw_job_stat", "id": "67928.12", "ordinal_position": 12, "name": "total_crashes", "default_value": null, "data_type": "bigint", "format": "int8", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67928, "schema": "_timescaledb_internal", "table": "bgw_job_stat", "id": "67928.13", "ordinal_position": 13, "name": "consecutive_failures", "default_value": null, "data_type": "integer", "format": "int4", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67928, "schema": "_timescaledb_internal", "table": "bgw_job_stat", "id": "67928.14", "ordinal_position": 14, "name": "consecutive_crashes", "default_value": null, "data_type": "integer", "format": "int4", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67928, "schema": "_timescaledb_internal", "table": "bgw_job_stat", "id": "67928.15", "ordinal_position": 15, "name": "flags", "default_value": "0", "data_type": "integer", "format": "int4", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}]}, {"id": 67941, "schema": "_timescaledb_internal", "name": "bgw_job_stat_history", "rls_enabled": false, "rls_forced": false, "replica_identity": "DEFAULT", "bytes": 81920, "size": "80 kB", "live_rows_estimate": 11, "dead_rows_estimate": 0, "comment": null, "primary_keys": [{"name": "id", "schema": "_timescaledb_internal", "table_id": 67941, "table_name": "bgw_job_stat_history"}], "relationships": [], "columns": [{"table_id": 67941, "schema": "_timescaledb_internal", "table": "bgw_job_stat_history", "id": "67941.1", "ordinal_position": 1, "name": "id", "default_value": "nextval('_timescaledb_internal.bgw_job_stat_history_id_seq'::regclass)", "data_type": "bigint", "format": "int8", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67941, "schema": "_timescaledb_internal", "table": "bgw_job_stat_history", "id": "67941.2", "ordinal_position": 2, "name": "job_id", "default_value": null, "data_type": "integer", "format": "int4", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67941, "schema": "_timescaledb_internal", "table": "bgw_job_stat_history", "id": "67941.3", "ordinal_position": 3, "name": "pid", "default_value": null, "data_type": "integer", "format": "int4", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67941, "schema": "_timescaledb_internal", "table": "bgw_job_stat_history", "id": "67941.4", "ordinal_position": 4, "name": "execution_start", "default_value": "now()", "data_type": "timestamp with time zone", "format": "timestamptz", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67941, "schema": "_timescaledb_internal", "table": "bgw_job_stat_history", "id": "67941.5", "ordinal_position": 5, "name": "execution_finish", "default_value": null, "data_type": "timestamp with time zone", "format": "timestamptz", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67941, "schema": "_timescaledb_internal", "table": "bgw_job_stat_history", "id": "67941.6", "ordinal_position": 6, "name": "succeeded", "default_value": "false", "data_type": "boolean", "format": "bool", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67941, "schema": "_timescaledb_internal", "table": "bgw_job_stat_history", "id": "67941.7", "ordinal_position": 7, "name": "data", "default_value": null, "data_type": "jsonb", "format": "jsonb", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}]}, {"id": 67952, "schema": "_timescaledb_internal", "name": "bgw_policy_chunk_stats", "rls_enabled": false, "rls_forced": false, "replica_identity": "DEFAULT", "bytes": 8192, "size": "8192 bytes", "live_rows_estimate": 0, "dead_rows_estimate": 0, "comment": null, "primary_keys": [], "relationships": [{"id": 67957, "source_schema": "_timescaledb_internal", "constraint_name": "bgw_policy_chunk_stats_chunk_id_fkey", "source_table_name": "bgw_policy_chunk_stats", "target_table_name": "chunk", "source_column_name": "chunk_id", "target_column_name": "id", "target_table_schema": "_timescaledb_catalog"}, {"id": 67962, "source_schema": "_timescaledb_internal", "constraint_name": "bgw_policy_chunk_stats_job_id_fkey", "source_table_name": "bgw_policy_chunk_stats", "target_table_name": "bgw_job", "source_column_name": "job_id", "target_column_name": "id", "target_table_schema": "_timescaledb_config"}], "columns": [{"table_id": 67952, "schema": "_timescaledb_internal", "table": "bgw_policy_chunk_stats", "id": "67952.1", "ordinal_position": 1, "name": "job_id", "default_value": null, "data_type": "integer", "format": "int4", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67952, "schema": "_timescaledb_internal", "table": "bgw_policy_chunk_stats", "id": "67952.2", "ordinal_position": 2, "name": "chunk_id", "default_value": null, "data_type": "integer", "format": "int4", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67952, "schema": "_timescaledb_internal", "table": "bgw_policy_chunk_stats", "id": "67952.3", "ordinal_position": 3, "name": "num_times_job_run", "default_value": null, "data_type": "integer", "format": "int4", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67952, "schema": "_timescaledb_internal", "table": "bgw_policy_chunk_stats", "id": "67952.4", "ordinal_position": 4, "name": "last_time_job_run", "default_value": null, "data_type": "timestamp with time zone", "format": "timestamptz", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}]}, {"id": 67967, "schema": "_timescaledb_catalog", "name": "metadata", "rls_enabled": false, "rls_forced": false, "replica_identity": "DEFAULT", "bytes": 32768, "size": "32 kB", "live_rows_estimate": 3, "dead_rows_estimate": 0, "comment": null, "primary_keys": [{"name": "key", "schema": "_timescaledb_catalog", "table_id": 67967, "table_name": "metadata"}], "relationships": [], "columns": [{"table_id": 67967, "schema": "_timescaledb_catalog", "table": "metadata", "id": "67967.1", "ordinal_position": 1, "name": "key", "default_value": null, "data_type": "name", "format": "name", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67967, "schema": "_timescaledb_catalog", "table": "metadata", "id": "67967.2", "ordinal_position": 2, "name": "value", "default_value": null, "data_type": "text", "format": "text", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67967, "schema": "_timescaledb_catalog", "table": "metadata", "id": "67967.3", "ordinal_position": 3, "name": "include_in_telemetry", "default_value": null, "data_type": "boolean", "format": "bool", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}]}, {"id": 67974, "schema": "_timescaledb_catalog", "name": "telemetry_event", "rls_enabled": false, "rls_forced": false, "replica_identity": "DEFAULT", "bytes": 8192, "size": "8192 bytes", "live_rows_estimate": 0, "dead_rows_estimate": 0, "comment": null, "primary_keys": [], "relationships": [], "columns": [{"table_id": 67974, "schema": "_timescaledb_catalog", "table": "telemetry_event", "id": "67974.1", "ordinal_position": 1, "name": "created", "default_value": "CURRENT_TIMESTAMP", "data_type": "timestamp with time zone", "format": "timestamptz", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67974, "schema": "_timescaledb_catalog", "table": "telemetry_event", "id": "67974.2", "ordinal_position": 2, "name": "tag", "default_value": null, "data_type": "name", "format": "name", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67974, "schema": "_timescaledb_catalog", "table": "telemetry_event", "id": "67974.3", "ordinal_position": 3, "name": "body", "default_value": null, "data_type": "jsonb", "format": "jsonb", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}]}, {"id": 67980, "schema": "_timescaledb_catalog", "name": "continuous_agg", "rls_enabled": false, "rls_forced": false, "replica_identity": "DEFAULT", "bytes": 32768, "size": "32 kB", "live_rows_estimate": 0, "dead_rows_estimate": 0, "comment": null, "primary_keys": [{"name": "mat_hypertable_id", "schema": "_timescaledb_catalog", "table_id": 67980, "table_name": "continuous_agg"}], "relationships": [{"id": 68001, "source_schema": "_timescaledb_catalog", "constraint_name": "continuous_agg_parent_mat_hypertable_id_fkey", "source_table_name": "continuous_agg", "target_table_name": "continuous_agg", "source_column_name": "parent_mat_hypertable_id", "target_column_name": "mat_hypertable_id", "target_table_schema": "_timescaledb_catalog"}, {"id": 68035, "source_schema": "_timescaledb_catalog", "constraint_name": "continuous_aggs_watermark_mat_hypertable_id_fkey", "source_table_name": "continuous_aggs_watermark", "target_table_name": "continuous_agg", "source_column_name": "mat_hypertable_id", "target_column_name": "mat_hypertable_id", "target_table_schema": "_timescaledb_catalog"}, {"id": 68047, "source_schema": "_timescaledb_catalog", "constraint_name": "continuous_aggs_materialization_invalid_materialization_id_fkey", "source_table_name": "continuous_aggs_materialization_invalidation_log", "target_table_name": "continuous_agg", "source_column_name": "materialization_id", "target_column_name": "mat_hypertable_id", "target_table_schema": "_timescaledb_catalog"}, {"id": 67991, "source_schema": "_timescaledb_catalog", "constraint_name": "continuous_agg_mat_hypertable_id_fkey", "source_table_name": "continuous_agg", "target_table_name": "hypertable", "source_column_name": "mat_hypertable_id", "target_column_name": "id", "target_table_schema": "_timescaledb_catalog"}, {"id": 67996, "source_schema": "_timescaledb_catalog", "constraint_name": "continuous_agg_raw_hypertable_id_fkey", "source_table_name": "continuous_agg", "target_table_name": "hypertable", "source_column_name": "raw_hypertable_id", "target_column_name": "id", "target_table_schema": "_timescaledb_catalog"}], "columns": [{"table_id": 67980, "schema": "_timescaledb_catalog", "table": "continuous_agg", "id": "67980.1", "ordinal_position": 1, "name": "mat_hypertable_id", "default_value": null, "data_type": "integer", "format": "int4", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67980, "schema": "_timescaledb_catalog", "table": "continuous_agg", "id": "67980.2", "ordinal_position": 2, "name": "raw_hypertable_id", "default_value": null, "data_type": "integer", "format": "int4", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67980, "schema": "_timescaledb_catalog", "table": "continuous_agg", "id": "67980.3", "ordinal_position": 3, "name": "parent_mat_hypertable_id", "default_value": null, "data_type": "integer", "format": "int4", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67980, "schema": "_timescaledb_catalog", "table": "continuous_agg", "id": "67980.4", "ordinal_position": 4, "name": "user_view_schema", "default_value": null, "data_type": "name", "format": "name", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67980, "schema": "_timescaledb_catalog", "table": "continuous_agg", "id": "67980.5", "ordinal_position": 5, "name": "user_view_name", "default_value": null, "data_type": "name", "format": "name", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67980, "schema": "_timescaledb_catalog", "table": "continuous_agg", "id": "67980.6", "ordinal_position": 6, "name": "partial_view_schema", "default_value": null, "data_type": "name", "format": "name", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67980, "schema": "_timescaledb_catalog", "table": "continuous_agg", "id": "67980.7", "ordinal_position": 7, "name": "partial_view_name", "default_value": null, "data_type": "name", "format": "name", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67980, "schema": "_timescaledb_catalog", "table": "continuous_agg", "id": "67980.8", "ordinal_position": 8, "name": "direct_view_schema", "default_value": null, "data_type": "name", "format": "name", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67980, "schema": "_timescaledb_catalog", "table": "continuous_agg", "id": "67980.9", "ordinal_position": 9, "name": "direct_view_name", "default_value": null, "data_type": "name", "format": "name", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67980, "schema": "_timescaledb_catalog", "table": "continuous_agg", "id": "67980.10", "ordinal_position": 10, "name": "materialized_only", "default_value": "false", "data_type": "boolean", "format": "bool", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 67980, "schema": "_timescaledb_catalog", "table": "continuous_agg", "id": "67980.11", "ordinal_position": 11, "name": "finalized", "default_value": "true", "data_type": "boolean", "format": "bool", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}]}, {"id": 68007, "schema": "_timescaledb_catalog", "name": "continuous_aggs_bucket_function", "rls_enabled": false, "rls_forced": false, "replica_identity": "DEFAULT", "bytes": 16384, "size": "16 kB", "live_rows_estimate": 0, "dead_rows_estimate": 0, "comment": null, "primary_keys": [{"name": "mat_hypertable_id", "schema": "_timescaledb_catalog", "table_id": 68007, "table_name": "continuous_aggs_bucket_function"}], "relationships": [{"id": 68015, "source_schema": "_timescaledb_catalog", "constraint_name": "continuous_aggs_bucket_function_mat_hypertable_id_fkey", "source_table_name": "continuous_aggs_bucket_function", "target_table_name": "hypertable", "source_column_name": "mat_hypertable_id", "target_column_name": "id", "target_table_schema": "_timescaledb_catalog"}], "columns": [{"table_id": 68007, "schema": "_timescaledb_catalog", "table": "continuous_aggs_bucket_function", "id": "68007.1", "ordinal_position": 1, "name": "mat_hypertable_id", "default_value": null, "data_type": "integer", "format": "int4", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 68007, "schema": "_timescaledb_catalog", "table": "continuous_aggs_bucket_function", "id": "68007.2", "ordinal_position": 2, "name": "bucket_func", "default_value": null, "data_type": "text", "format": "text", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": "to_regprocedure(bucket_func)::oid IS DISTINCT FROM 0::oid", "enums": [], "comment": null}, {"table_id": 68007, "schema": "_timescaledb_catalog", "table": "continuous_aggs_bucket_function", "id": "68007.3", "ordinal_position": 3, "name": "bucket_width", "default_value": null, "data_type": "text", "format": "text", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 68007, "schema": "_timescaledb_catalog", "table": "continuous_aggs_bucket_function", "id": "68007.4", "ordinal_position": 4, "name": "bucket_origin", "default_value": null, "data_type": "text", "format": "text", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 68007, "schema": "_timescaledb_catalog", "table": "continuous_aggs_bucket_function", "id": "68007.5", "ordinal_position": 5, "name": "bucket_offset", "default_value": null, "data_type": "text", "format": "text", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 68007, "schema": "_timescaledb_catalog", "table": "continuous_aggs_bucket_function", "id": "68007.6", "ordinal_position": 6, "name": "bucket_timezone", "default_value": null, "data_type": "text", "format": "text", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 68007, "schema": "_timescaledb_catalog", "table": "continuous_aggs_bucket_function", "id": "68007.7", "ordinal_position": 7, "name": "bucket_fixed_width", "default_value": null, "data_type": "boolean", "format": "bool", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}]}, {"id": 68020, "schema": "_timescaledb_catalog", "name": "continuous_aggs_invalidation_threshold", "rls_enabled": false, "rls_forced": false, "replica_identity": "DEFAULT", "bytes": 8192, "size": "8192 bytes", "live_rows_estimate": 0, "dead_rows_estimate": 0, "comment": null, "primary_keys": [{"name": "hypertable_id", "schema": "_timescaledb_catalog", "table_id": 68020, "table_name": "continuous_aggs_invalidation_threshold"}], "relationships": [{"id": 68025, "source_schema": "_timescaledb_catalog", "constraint_name": "continuous_aggs_invalidation_threshold_hypertable_id_fkey", "source_table_name": "continuous_aggs_invalidation_threshold", "target_table_name": "hypertable", "source_column_name": "hypertable_id", "target_column_name": "id", "target_table_schema": "_timescaledb_catalog"}], "columns": [{"table_id": 68020, "schema": "_timescaledb_catalog", "table": "continuous_aggs_invalidation_threshold", "id": "68020.1", "ordinal_position": 1, "name": "hypertable_id", "default_value": null, "data_type": "integer", "format": "int4", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 68020, "schema": "_timescaledb_catalog", "table": "continuous_aggs_invalidation_threshold", "id": "68020.2", "ordinal_position": 2, "name": "watermark", "default_value": null, "data_type": "bigint", "format": "int8", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}]}, {"id": 68030, "schema": "_timescaledb_catalog", "name": "continuous_aggs_watermark", "rls_enabled": false, "rls_forced": false, "replica_identity": "DEFAULT", "bytes": 8192, "size": "8192 bytes", "live_rows_estimate": 0, "dead_rows_estimate": 0, "comment": null, "primary_keys": [{"name": "mat_hypertable_id", "schema": "_timescaledb_catalog", "table_id": 68030, "table_name": "continuous_aggs_watermark"}], "relationships": [{"id": 68035, "source_schema": "_timescaledb_catalog", "constraint_name": "continuous_aggs_watermark_mat_hypertable_id_fkey", "source_table_name": "continuous_aggs_watermark", "target_table_name": "continuous_agg", "source_column_name": "mat_hypertable_id", "target_column_name": "mat_hypertable_id", "target_table_schema": "_timescaledb_catalog"}], "columns": [{"table_id": 68030, "schema": "_timescaledb_catalog", "table": "continuous_aggs_watermark", "id": "68030.1", "ordinal_position": 1, "name": "mat_hypertable_id", "default_value": null, "data_type": "integer", "format": "int4", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 68030, "schema": "_timescaledb_catalog", "table": "continuous_aggs_watermark", "id": "68030.2", "ordinal_position": 2, "name": "watermark", "default_value": null, "data_type": "bigint", "format": "int8", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}]}, {"id": 68040, "schema": "_timescaledb_catalog", "name": "continuous_aggs_hypertable_invalidation_log", "rls_enabled": false, "rls_forced": false, "replica_identity": "DEFAULT", "bytes": 8192, "size": "8192 bytes", "live_rows_estimate": 0, "dead_rows_estimate": 0, "comment": null, "primary_keys": [], "relationships": [], "columns": [{"table_id": 68040, "schema": "_timescaledb_catalog", "table": "continuous_aggs_hypertable_invalidation_log", "id": "68040.1", "ordinal_position": 1, "name": "hypertable_id", "default_value": null, "data_type": "integer", "format": "int4", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 68040, "schema": "_timescaledb_catalog", "table": "continuous_aggs_hypertable_invalidation_log", "id": "68040.2", "ordinal_position": 2, "name": "lowest_modified_value", "default_value": null, "data_type": "bigint", "format": "int8", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 68040, "schema": "_timescaledb_catalog", "table": "continuous_aggs_hypertable_invalidation_log", "id": "68040.3", "ordinal_position": 3, "name": "greatest_modified_value", "default_value": null, "data_type": "bigint", "format": "int8", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}]}, {"id": 68044, "schema": "_timescaledb_catalog", "name": "continuous_aggs_materialization_invalidation_log", "rls_enabled": false, "rls_forced": false, "replica_identity": "DEFAULT", "bytes": 8192, "size": "8192 bytes", "live_rows_estimate": 0, "dead_rows_estimate": 0, "comment": null, "primary_keys": [], "relationships": [{"id": 68047, "source_schema": "_timescaledb_catalog", "constraint_name": "continuous_aggs_materialization_invalid_materialization_id_fkey", "source_table_name": "continuous_aggs_materialization_invalidation_log", "target_table_name": "continuous_agg", "source_column_name": "materialization_id", "target_column_name": "mat_hypertable_id", "target_table_schema": "_timescaledb_catalog"}], "columns": [{"table_id": 68044, "schema": "_timescaledb_catalog", "table": "continuous_aggs_materialization_invalidation_log", "id": "68044.1", "ordinal_position": 1, "name": "materialization_id", "default_value": null, "data_type": "integer", "format": "int4", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 68044, "schema": "_timescaledb_catalog", "table": "continuous_aggs_materialization_invalidation_log", "id": "68044.2", "ordinal_position": 2, "name": "lowest_modified_value", "default_value": null, "data_type": "bigint", "format": "int8", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 68044, "schema": "_timescaledb_catalog", "table": "continuous_aggs_materialization_invalidation_log", "id": "68044.3", "ordinal_position": 3, "name": "greatest_modified_value", "default_value": null, "data_type": "bigint", "format": "int8", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}]}, {"id": 68053, "schema": "_timescaledb_catalog", "name": "compression_algorithm", "rls_enabled": false, "rls_forced": false, "replica_identity": "DEFAULT", "bytes": 32768, "size": "32 kB", "live_rows_estimate": 5, "dead_rows_estimate": 0, "comment": null, "primary_keys": [{"name": "id", "schema": "_timescaledb_catalog", "table_id": 68053, "table_name": "compression_algorithm"}], "relationships": [], "columns": [{"table_id": 68053, "schema": "_timescaledb_catalog", "table": "compression_algorithm", "id": "68053.1", "ordinal_position": 1, "name": "id", "default_value": null, "data_type": "smallint", "format": "int2", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 68053, "schema": "_timescaledb_catalog", "table": "compression_algorithm", "id": "68053.2", "ordinal_position": 2, "name": "version", "default_value": null, "data_type": "smallint", "format": "int2", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 68053, "schema": "_timescaledb_catalog", "table": "compression_algorithm", "id": "68053.3", "ordinal_position": 3, "name": "name", "default_value": null, "data_type": "name", "format": "name", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 68053, "schema": "_timescaledb_catalog", "table": "compression_algorithm", "id": "68053.4", "ordinal_position": 4, "name": "description", "default_value": null, "data_type": "text", "format": "text", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}]}, {"id": 68060, "schema": "_timescaledb_catalog", "name": "compression_settings", "rls_enabled": false, "rls_forced": false, "replica_identity": "DEFAULT", "bytes": 16384, "size": "16 kB", "live_rows_estimate": 0, "dead_rows_estimate": 0, "comment": null, "primary_keys": [{"name": "relid", "schema": "_timescaledb_catalog", "table_id": 68060, "table_name": "compression_settings"}], "relationships": [], "columns": [{"table_id": 68060, "schema": "_timescaledb_catalog", "table": "compression_settings", "id": "68060.1", "ordinal_position": 1, "name": "relid", "default_value": null, "data_type": "regclass", "format": "regclass", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 68060, "schema": "_timescaledb_catalog", "table": "compression_settings", "id": "68060.2", "ordinal_position": 2, "name": "segmentby", "default_value": null, "data_type": "ARRAY", "format": "_text", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": "array_ndims(segmentby) = 1", "enums": [], "comment": null}, {"table_id": 68060, "schema": "_timescaledb_catalog", "table": "compression_settings", "id": "68060.3", "ordinal_position": 3, "name": "orderby", "default_value": null, "data_type": "ARRAY", "format": "_text", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 68060, "schema": "_timescaledb_catalog", "table": "compression_settings", "id": "68060.4", "ordinal_position": 4, "name": "orderby_desc", "default_value": null, "data_type": "ARRAY", "format": "_bool", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 68060, "schema": "_timescaledb_catalog", "table": "compression_settings", "id": "68060.5", "ordinal_position": 5, "name": "orderby_nullsfirst", "default_value": null, "data_type": "ARRAY", "format": "_bool", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}]}, {"id": 68070, "schema": "_timescaledb_catalog", "name": "compression_chunk_size", "rls_enabled": false, "rls_forced": false, "replica_identity": "DEFAULT", "bytes": 8192, "size": "8192 bytes", "live_rows_estimate": 0, "dead_rows_estimate": 0, "comment": null, "primary_keys": [{"name": "chunk_id", "schema": "_timescaledb_catalog", "table_id": 68070, "table_name": "compression_chunk_size"}], "relationships": [{"id": 68075, "source_schema": "_timescaledb_catalog", "constraint_name": "compression_chunk_size_chunk_id_fkey", "source_table_name": "compression_chunk_size", "target_table_name": "chunk", "source_column_name": "chunk_id", "target_column_name": "id", "target_table_schema": "_timescaledb_catalog"}, {"id": 68080, "source_schema": "_timescaledb_catalog", "constraint_name": "compression_chunk_size_compressed_chunk_id_fkey", "source_table_name": "compression_chunk_size", "target_table_name": "chunk", "source_column_name": "compressed_chunk_id", "target_column_name": "id", "target_table_schema": "_timescaledb_catalog"}], "columns": [{"table_id": 68070, "schema": "_timescaledb_catalog", "table": "compression_chunk_size", "id": "68070.1", "ordinal_position": 1, "name": "chunk_id", "default_value": null, "data_type": "integer", "format": "int4", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 68070, "schema": "_timescaledb_catalog", "table": "compression_chunk_size", "id": "68070.2", "ordinal_position": 2, "name": "compressed_chunk_id", "default_value": null, "data_type": "integer", "format": "int4", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 68070, "schema": "_timescaledb_catalog", "table": "compression_chunk_size", "id": "68070.3", "ordinal_position": 3, "name": "uncompressed_heap_size", "default_value": null, "data_type": "bigint", "format": "int8", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 68070, "schema": "_timescaledb_catalog", "table": "compression_chunk_size", "id": "68070.4", "ordinal_position": 4, "name": "uncompressed_toast_size", "default_value": null, "data_type": "bigint", "format": "int8", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 68070, "schema": "_timescaledb_catalog", "table": "compression_chunk_size", "id": "68070.5", "ordinal_position": 5, "name": "uncompressed_index_size", "default_value": null, "data_type": "bigint", "format": "int8", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 68070, "schema": "_timescaledb_catalog", "table": "compression_chunk_size", "id": "68070.6", "ordinal_position": 6, "name": "compressed_heap_size", "default_value": null, "data_type": "bigint", "format": "int8", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 68070, "schema": "_timescaledb_catalog", "table": "compression_chunk_size", "id": "68070.7", "ordinal_position": 7, "name": "compressed_toast_size", "default_value": null, "data_type": "bigint", "format": "int8", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 68070, "schema": "_timescaledb_catalog", "table": "compression_chunk_size", "id": "68070.8", "ordinal_position": 8, "name": "compressed_index_size", "default_value": null, "data_type": "bigint", "format": "int8", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 68070, "schema": "_timescaledb_catalog", "table": "compression_chunk_size", "id": "68070.9", "ordinal_position": 9, "name": "numrows_pre_compression", "default_value": null, "data_type": "bigint", "format": "int8", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 68070, "schema": "_timescaledb_catalog", "table": "compression_chunk_size", "id": "68070.10", "ordinal_position": 10, "name": "numrows_post_compression", "default_value": null, "data_type": "bigint", "format": "int8", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 68070, "schema": "_timescaledb_catalog", "table": "compression_chunk_size", "id": "68070.11", "ordinal_position": 11, "name": "numrows_frozen_immediately", "default_value": null, "data_type": "bigint", "format": "int8", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}]}, {"id": 68085, "schema": "_timescaledb_catalog", "name": "continuous_agg_migrate_plan", "rls_enabled": false, "rls_forced": false, "replica_identity": "DEFAULT", "bytes": 16384, "size": "16 kB", "live_rows_estimate": 0, "dead_rows_estimate": 0, "comment": null, "primary_keys": [{"name": "mat_hypertable_id", "schema": "_timescaledb_catalog", "table_id": 68085, "table_name": "continuous_agg_migrate_plan"}], "relationships": [{"id": 68105, "source_schema": "_timescaledb_catalog", "constraint_name": "continuous_agg_migrate_plan_step_mat_hypertable_id_fkey", "source_table_name": "continuous_agg_migrate_plan_step", "target_table_name": "continuous_agg_migrate_plan", "source_column_name": "mat_hypertable_id", "target_column_name": "mat_hypertable_id", "target_table_schema": "_timescaledb_catalog"}], "columns": [{"table_id": 68085, "schema": "_timescaledb_catalog", "table": "continuous_agg_migrate_plan", "id": "68085.1", "ordinal_position": 1, "name": "mat_hypertable_id", "default_value": null, "data_type": "integer", "format": "int4", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 68085, "schema": "_timescaledb_catalog", "table": "continuous_agg_migrate_plan", "id": "68085.2", "ordinal_position": 2, "name": "start_ts", "default_value": "now()", "data_type": "timestamp with time zone", "format": "timestamptz", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 68085, "schema": "_timescaledb_catalog", "table": "continuous_agg_migrate_plan", "id": "68085.3", "ordinal_position": 3, "name": "end_ts", "default_value": null, "data_type": "timestamp with time zone", "format": "timestamptz", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 68085, "schema": "_timescaledb_catalog", "table": "continuous_agg_migrate_plan", "id": "68085.4", "ordinal_position": 4, "name": "user_view_definition", "default_value": null, "data_type": "text", "format": "text", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}]}, {"id": 68094, "schema": "_timescaledb_catalog", "name": "continuous_agg_migrate_plan_step", "rls_enabled": false, "rls_forced": false, "replica_identity": "DEFAULT", "bytes": 16384, "size": "16 kB", "live_rows_estimate": 0, "dead_rows_estimate": 0, "comment": null, "primary_keys": [{"name": "mat_hypertable_id", "schema": "_timescaledb_catalog", "table_id": 68094, "table_name": "continuous_agg_migrate_plan_step"}, {"name": "step_id", "schema": "_timescaledb_catalog", "table_id": 68094, "table_name": "continuous_agg_migrate_plan_step"}], "relationships": [{"id": 68105, "source_schema": "_timescaledb_catalog", "constraint_name": "continuous_agg_migrate_plan_step_mat_hypertable_id_fkey", "source_table_name": "continuous_agg_migrate_plan_step", "target_table_name": "continuous_agg_migrate_plan", "source_column_name": "mat_hypertable_id", "target_column_name": "mat_hypertable_id", "target_table_schema": "_timescaledb_catalog"}], "columns": [{"table_id": 68094, "schema": "_timescaledb_catalog", "table": "continuous_agg_migrate_plan_step", "id": "68094.1", "ordinal_position": 1, "name": "mat_hypertable_id", "default_value": null, "data_type": "integer", "format": "int4", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 68094, "schema": "_timescaledb_catalog", "table": "continuous_agg_migrate_plan_step", "id": "68094.2", "ordinal_position": 2, "name": "step_id", "default_value": "nextval('_timescaledb_catalog.continuous_agg_migrate_plan_step_step_id_seq'::regclass)", "data_type": "integer", "format": "int4", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 68094, "schema": "_timescaledb_catalog", "table": "continuous_agg_migrate_plan_step", "id": "68094.3", "ordinal_position": 3, "name": "status", "default_value": "'NOT STARTED'::text", "data_type": "text", "format": "text", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 68094, "schema": "_timescaledb_catalog", "table": "continuous_agg_migrate_plan_step", "id": "68094.4", "ordinal_position": 4, "name": "start_ts", "default_value": null, "data_type": "timestamp with time zone", "format": "timestamptz", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 68094, "schema": "_timescaledb_catalog", "table": "continuous_agg_migrate_plan_step", "id": "68094.5", "ordinal_position": 5, "name": "end_ts", "default_value": null, "data_type": "timestamp with time zone", "format": "timestamptz", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 68094, "schema": "_timescaledb_catalog", "table": "continuous_agg_migrate_plan_step", "id": "68094.6", "ordinal_position": 6, "name": "type", "default_value": null, "data_type": "text", "format": "text", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": "type = ANY (ARRAY['CREATE NEW CAGG'::text, 'DISABLE POLICIES'::text, 'COPY POLICIES'::text, 'ENABLE POLICIES'::text, 'SAVE WATERMARK'::text, 'REFRESH NEW CAGG'::text, 'COPY DATA'::text, 'OVERRIDE CAGG'::text, 'DROP OLD CAGG'::text])", "enums": [], "comment": null}, {"table_id": 68094, "schema": "_timescaledb_catalog", "table": "continuous_agg_migrate_plan_step", "id": "68094.7", "ordinal_position": 7, "name": "config", "default_value": null, "data_type": "jsonb", "format": "jsonb", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}]}, {"id": 68110, "schema": "_timescaledb_cache", "name": "cache_inval_hypertable", "rls_enabled": false, "rls_forced": false, "replica_identity": "DEFAULT", "bytes": 0, "size": "0 bytes", "live_rows_estimate": 0, "dead_rows_estimate": 0, "comment": null, "primary_keys": [], "relationships": [], "columns": []}, {"id": 68113, "schema": "_timescaledb_cache", "name": "cache_inval_bgw_job", "rls_enabled": false, "rls_forced": false, "replica_identity": "DEFAULT", "bytes": 0, "size": "0 bytes", "live_rows_estimate": 0, "dead_rows_estimate": 0, "comment": null, "primary_keys": [], "relationships": [], "columns": []}, {"id": 68116, "schema": "_timescaledb_cache", "name": "cache_inval_extension", "rls_enabled": false, "rls_forced": false, "replica_identity": "DEFAULT", "bytes": 0, "size": "0 bytes", "live_rows_estimate": 0, "dead_rows_estimate": 0, "comment": null, "primary_keys": [], "relationships": [], "columns": []}, {"id": 70693, "schema": "supabase_migrations", "name": "schema_migrations", "rls_enabled": false, "rls_forced": false, "replica_identity": "DEFAULT", "bytes": 65536, "size": "64 kB", "live_rows_estimate": 6, "dead_rows_estimate": 0, "comment": null, "primary_keys": [{"name": "version", "schema": "supabase_migrations", "table_id": 70693, "table_name": "schema_migrations"}], "relationships": [], "columns": [{"table_id": 70693, "schema": "supabase_migrations", "table": "schema_migrations", "id": "70693.1", "ordinal_position": 1, "name": "version", "default_value": null, "data_type": "text", "format": "text", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 70693, "schema": "supabase_migrations", "table": "schema_migrations", "id": "70693.2", "ordinal_position": 2, "name": "statements", "default_value": null, "data_type": "ARRAY", "format": "_text", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 70693, "schema": "supabase_migrations", "table": "schema_migrations", "id": "70693.3", "ordinal_position": 3, "name": "name", "default_value": null, "data_type": "text", "format": "text", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 70693, "schema": "supabase_migrations", "table": "schema_migrations", "id": "70693.4", "ordinal_position": 4, "name": "created_by", "default_value": null, "data_type": "text", "format": "text", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}]}, {"id": 70700, "schema": "public", "name": "nifty_timeseries", "rls_enabled": false, "rls_forced": false, "replica_identity": "DEFAULT", "bytes": 40960, "size": "40 kB", "live_rows_estimate": 0, "dead_rows_estimate": 0, "comment": null, "primary_keys": [{"name": "timestamp", "schema": "public", "table_id": 70700, "table_name": "nifty_timeseries"}, {"name": "expiry_date", "schema": "public", "table_id": 70700, "table_name": "nifty_timeseries"}, {"name": "strike_price", "schema": "public", "table_id": 70700, "table_name": "nifty_timeseries"}, {"name": "option_type", "schema": "public", "table_id": 70700, "table_name": "nifty_timeseries"}], "relationships": [], "columns": [{"table_id": 70700, "schema": "public", "table": "nifty_timeseries", "id": "70700.1", "ordinal_position": 1, "name": "timestamp", "default_value": null, "data_type": "timestamp with time zone", "format": "timestamptz", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 70700, "schema": "public", "table": "nifty_timeseries", "id": "70700.2", "ordinal_position": 2, "name": "expiry_date", "default_value": null, "data_type": "date", "format": "date", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 70700, "schema": "public", "table": "nifty_timeseries", "id": "70700.3", "ordinal_position": 3, "name": "strike_price", "default_value": null, "data_type": "numeric", "format": "numeric", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 70700, "schema": "public", "table": "nifty_timeseries", "id": "70700.4", "ordinal_position": 4, "name": "spot_price", "default_value": null, "data_type": "numeric", "format": "numeric", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 70700, "schema": "public", "table": "nifty_timeseries", "id": "70700.5", "ordinal_position": 5, "name": "option_type", "default_value": null, "data_type": "character varying", "format": "<PERSON><PERSON><PERSON>", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 70700, "schema": "public", "table": "nifty_timeseries", "id": "70700.6", "ordinal_position": 6, "name": "last_price", "default_value": null, "data_type": "numeric", "format": "numeric", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 70700, "schema": "public", "table": "nifty_timeseries", "id": "70700.7", "ordinal_position": 7, "name": "oi", "default_value": null, "data_type": "integer", "format": "int4", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 70700, "schema": "public", "table": "nifty_timeseries", "id": "70700.8", "ordinal_position": 8, "name": "volume", "default_value": null, "data_type": "integer", "format": "int4", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 70700, "schema": "public", "table": "nifty_timeseries", "id": "70700.9", "ordinal_position": 9, "name": "iv", "default_value": null, "data_type": "numeric", "format": "numeric", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 70700, "schema": "public", "table": "nifty_timeseries", "id": "70700.10", "ordinal_position": 10, "name": "delta", "default_value": null, "data_type": "numeric", "format": "numeric", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 70700, "schema": "public", "table": "nifty_timeseries", "id": "70700.11", "ordinal_position": 11, "name": "theta", "default_value": null, "data_type": "numeric", "format": "numeric", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 70700, "schema": "public", "table": "nifty_timeseries", "id": "70700.12", "ordinal_position": 12, "name": "gamma", "default_value": null, "data_type": "numeric", "format": "numeric", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 70700, "schema": "public", "table": "nifty_timeseries", "id": "70700.13", "ordinal_position": 13, "name": "vega", "default_value": null, "data_type": "numeric", "format": "numeric", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}]}, {"id": 70730, "schema": "realtime", "name": "messages_2025_04_10", "rls_enabled": false, "rls_forced": false, "replica_identity": "DEFAULT", "bytes": 16384, "size": "16 kB", "live_rows_estimate": 0, "dead_rows_estimate": 0, "comment": null, "primary_keys": [{"name": "inserted_at", "schema": "realtime", "table_id": 70730, "table_name": "messages_2025_04_10"}, {"name": "id", "schema": "realtime", "table_id": 70730, "table_name": "messages_2025_04_10"}], "relationships": [], "columns": [{"table_id": 70730, "schema": "realtime", "table": "messages_2025_04_10", "id": "70730.1", "ordinal_position": 1, "name": "topic", "default_value": null, "data_type": "text", "format": "text", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 70730, "schema": "realtime", "table": "messages_2025_04_10", "id": "70730.2", "ordinal_position": 2, "name": "extension", "default_value": null, "data_type": "text", "format": "text", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 70730, "schema": "realtime", "table": "messages_2025_04_10", "id": "70730.3", "ordinal_position": 3, "name": "payload", "default_value": null, "data_type": "jsonb", "format": "jsonb", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 70730, "schema": "realtime", "table": "messages_2025_04_10", "id": "70730.4", "ordinal_position": 4, "name": "event", "default_value": null, "data_type": "text", "format": "text", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 70730, "schema": "realtime", "table": "messages_2025_04_10", "id": "70730.5", "ordinal_position": 5, "name": "private", "default_value": "false", "data_type": "boolean", "format": "bool", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 70730, "schema": "realtime", "table": "messages_2025_04_10", "id": "70730.6", "ordinal_position": 6, "name": "updated_at", "default_value": "now()", "data_type": "timestamp without time zone", "format": "timestamp", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 70730, "schema": "realtime", "table": "messages_2025_04_10", "id": "70730.7", "ordinal_position": 7, "name": "inserted_at", "default_value": "now()", "data_type": "timestamp without time zone", "format": "timestamp", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 70730, "schema": "realtime", "table": "messages_2025_04_10", "id": "70730.8", "ordinal_position": 8, "name": "id", "default_value": "gen_random_uuid()", "data_type": "uuid", "format": "uuid", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}]}, {"id": 70741, "schema": "realtime", "name": "messages_2025_04_11", "rls_enabled": false, "rls_forced": false, "replica_identity": "DEFAULT", "bytes": 16384, "size": "16 kB", "live_rows_estimate": 0, "dead_rows_estimate": 0, "comment": null, "primary_keys": [{"name": "inserted_at", "schema": "realtime", "table_id": 70741, "table_name": "messages_2025_04_11"}, {"name": "id", "schema": "realtime", "table_id": 70741, "table_name": "messages_2025_04_11"}], "relationships": [], "columns": [{"table_id": 70741, "schema": "realtime", "table": "messages_2025_04_11", "id": "70741.1", "ordinal_position": 1, "name": "topic", "default_value": null, "data_type": "text", "format": "text", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 70741, "schema": "realtime", "table": "messages_2025_04_11", "id": "70741.2", "ordinal_position": 2, "name": "extension", "default_value": null, "data_type": "text", "format": "text", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 70741, "schema": "realtime", "table": "messages_2025_04_11", "id": "70741.3", "ordinal_position": 3, "name": "payload", "default_value": null, "data_type": "jsonb", "format": "jsonb", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 70741, "schema": "realtime", "table": "messages_2025_04_11", "id": "70741.4", "ordinal_position": 4, "name": "event", "default_value": null, "data_type": "text", "format": "text", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 70741, "schema": "realtime", "table": "messages_2025_04_11", "id": "70741.5", "ordinal_position": 5, "name": "private", "default_value": "false", "data_type": "boolean", "format": "bool", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 70741, "schema": "realtime", "table": "messages_2025_04_11", "id": "70741.6", "ordinal_position": 6, "name": "updated_at", "default_value": "now()", "data_type": "timestamp without time zone", "format": "timestamp", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 70741, "schema": "realtime", "table": "messages_2025_04_11", "id": "70741.7", "ordinal_position": 7, "name": "inserted_at", "default_value": "now()", "data_type": "timestamp without time zone", "format": "timestamp", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 70741, "schema": "realtime", "table": "messages_2025_04_11", "id": "70741.8", "ordinal_position": 8, "name": "id", "default_value": "gen_random_uuid()", "data_type": "uuid", "format": "uuid", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}]}, {"id": 70752, "schema": "realtime", "name": "messages_2025_04_12", "rls_enabled": false, "rls_forced": false, "replica_identity": "DEFAULT", "bytes": 16384, "size": "16 kB", "live_rows_estimate": 0, "dead_rows_estimate": 0, "comment": null, "primary_keys": [{"name": "inserted_at", "schema": "realtime", "table_id": 70752, "table_name": "messages_2025_04_12"}, {"name": "id", "schema": "realtime", "table_id": 70752, "table_name": "messages_2025_04_12"}], "relationships": [], "columns": [{"table_id": 70752, "schema": "realtime", "table": "messages_2025_04_12", "id": "70752.1", "ordinal_position": 1, "name": "topic", "default_value": null, "data_type": "text", "format": "text", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 70752, "schema": "realtime", "table": "messages_2025_04_12", "id": "70752.2", "ordinal_position": 2, "name": "extension", "default_value": null, "data_type": "text", "format": "text", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 70752, "schema": "realtime", "table": "messages_2025_04_12", "id": "70752.3", "ordinal_position": 3, "name": "payload", "default_value": null, "data_type": "jsonb", "format": "jsonb", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 70752, "schema": "realtime", "table": "messages_2025_04_12", "id": "70752.4", "ordinal_position": 4, "name": "event", "default_value": null, "data_type": "text", "format": "text", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 70752, "schema": "realtime", "table": "messages_2025_04_12", "id": "70752.5", "ordinal_position": 5, "name": "private", "default_value": "false", "data_type": "boolean", "format": "bool", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 70752, "schema": "realtime", "table": "messages_2025_04_12", "id": "70752.6", "ordinal_position": 6, "name": "updated_at", "default_value": "now()", "data_type": "timestamp without time zone", "format": "timestamp", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 70752, "schema": "realtime", "table": "messages_2025_04_12", "id": "70752.7", "ordinal_position": 7, "name": "inserted_at", "default_value": "now()", "data_type": "timestamp without time zone", "format": "timestamp", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 70752, "schema": "realtime", "table": "messages_2025_04_12", "id": "70752.8", "ordinal_position": 8, "name": "id", "default_value": "gen_random_uuid()", "data_type": "uuid", "format": "uuid", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}]}, {"id": 70763, "schema": "realtime", "name": "messages_2025_04_13", "rls_enabled": false, "rls_forced": false, "replica_identity": "DEFAULT", "bytes": 16384, "size": "16 kB", "live_rows_estimate": 0, "dead_rows_estimate": 0, "comment": null, "primary_keys": [{"name": "inserted_at", "schema": "realtime", "table_id": 70763, "table_name": "messages_2025_04_13"}, {"name": "id", "schema": "realtime", "table_id": 70763, "table_name": "messages_2025_04_13"}], "relationships": [], "columns": [{"table_id": 70763, "schema": "realtime", "table": "messages_2025_04_13", "id": "70763.1", "ordinal_position": 1, "name": "topic", "default_value": null, "data_type": "text", "format": "text", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 70763, "schema": "realtime", "table": "messages_2025_04_13", "id": "70763.2", "ordinal_position": 2, "name": "extension", "default_value": null, "data_type": "text", "format": "text", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 70763, "schema": "realtime", "table": "messages_2025_04_13", "id": "70763.3", "ordinal_position": 3, "name": "payload", "default_value": null, "data_type": "jsonb", "format": "jsonb", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 70763, "schema": "realtime", "table": "messages_2025_04_13", "id": "70763.4", "ordinal_position": 4, "name": "event", "default_value": null, "data_type": "text", "format": "text", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 70763, "schema": "realtime", "table": "messages_2025_04_13", "id": "70763.5", "ordinal_position": 5, "name": "private", "default_value": "false", "data_type": "boolean", "format": "bool", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 70763, "schema": "realtime", "table": "messages_2025_04_13", "id": "70763.6", "ordinal_position": 6, "name": "updated_at", "default_value": "now()", "data_type": "timestamp without time zone", "format": "timestamp", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 70763, "schema": "realtime", "table": "messages_2025_04_13", "id": "70763.7", "ordinal_position": 7, "name": "inserted_at", "default_value": "now()", "data_type": "timestamp without time zone", "format": "timestamp", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 70763, "schema": "realtime", "table": "messages_2025_04_13", "id": "70763.8", "ordinal_position": 8, "name": "id", "default_value": "gen_random_uuid()", "data_type": "uuid", "format": "uuid", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}]}, {"id": 70774, "schema": "realtime", "name": "messages_2025_04_14", "rls_enabled": false, "rls_forced": false, "replica_identity": "DEFAULT", "bytes": 16384, "size": "16 kB", "live_rows_estimate": 0, "dead_rows_estimate": 0, "comment": null, "primary_keys": [{"name": "inserted_at", "schema": "realtime", "table_id": 70774, "table_name": "messages_2025_04_14"}, {"name": "id", "schema": "realtime", "table_id": 70774, "table_name": "messages_2025_04_14"}], "relationships": [], "columns": [{"table_id": 70774, "schema": "realtime", "table": "messages_2025_04_14", "id": "70774.1", "ordinal_position": 1, "name": "topic", "default_value": null, "data_type": "text", "format": "text", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 70774, "schema": "realtime", "table": "messages_2025_04_14", "id": "70774.2", "ordinal_position": 2, "name": "extension", "default_value": null, "data_type": "text", "format": "text", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 70774, "schema": "realtime", "table": "messages_2025_04_14", "id": "70774.3", "ordinal_position": 3, "name": "payload", "default_value": null, "data_type": "jsonb", "format": "jsonb", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 70774, "schema": "realtime", "table": "messages_2025_04_14", "id": "70774.4", "ordinal_position": 4, "name": "event", "default_value": null, "data_type": "text", "format": "text", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 70774, "schema": "realtime", "table": "messages_2025_04_14", "id": "70774.5", "ordinal_position": 5, "name": "private", "default_value": "false", "data_type": "boolean", "format": "bool", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": true, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 70774, "schema": "realtime", "table": "messages_2025_04_14", "id": "70774.6", "ordinal_position": 6, "name": "updated_at", "default_value": "now()", "data_type": "timestamp without time zone", "format": "timestamp", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 70774, "schema": "realtime", "table": "messages_2025_04_14", "id": "70774.7", "ordinal_position": 7, "name": "inserted_at", "default_value": "now()", "data_type": "timestamp without time zone", "format": "timestamp", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 70774, "schema": "realtime", "table": "messages_2025_04_14", "id": "70774.8", "ordinal_position": 8, "name": "id", "default_value": "gen_random_uuid()", "data_type": "uuid", "format": "uuid", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}]}, {"id": 70794, "schema": "public", "name": "expiry_dates", "rls_enabled": false, "rls_forced": false, "replica_identity": "DEFAULT", "bytes": 32768, "size": "32 kB", "live_rows_estimate": 0, "dead_rows_estimate": 0, "comment": null, "primary_keys": [{"name": "id", "schema": "public", "table_id": 70794, "table_name": "expiry_dates"}], "relationships": [], "columns": [{"table_id": 70794, "schema": "public", "table": "expiry_dates", "id": "70794.1", "ordinal_position": 1, "name": "id", "default_value": "nextval('expiry_dates_id_seq'::regclass)", "data_type": "bigint", "format": "int8", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 70794, "schema": "public", "table": "expiry_dates", "id": "70794.2", "ordinal_position": 2, "name": "expiry_date", "default_value": null, "data_type": "date", "format": "date", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": true, "check": null, "enums": [], "comment": null}, {"table_id": 70794, "schema": "public", "table": "expiry_dates", "id": "70794.3", "ordinal_position": 3, "name": "is_weekly", "default_value": "false", "data_type": "boolean", "format": "bool", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 70794, "schema": "public", "table": "expiry_dates", "id": "70794.4", "ordinal_position": 4, "name": "is_monthly", "default_value": "false", "data_type": "boolean", "format": "bool", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 70794, "schema": "public", "table": "expiry_dates", "id": "70794.5", "ordinal_position": 5, "name": "status", "default_value": "'active'::character varying", "data_type": "character varying", "format": "<PERSON><PERSON><PERSON>", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 70794, "schema": "public", "table": "expiry_dates", "id": "70794.6", "ordinal_position": 6, "name": "created_at", "default_value": "now()", "data_type": "timestamp with time zone", "format": "timestamptz", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}, {"table_id": 70794, "schema": "public", "table": "expiry_dates", "id": "70794.7", "ordinal_position": 7, "name": "updated_at", "default_value": "now()", "data_type": "timestamp with time zone", "format": "timestamptz", "is_identity": false, "identity_generation": null, "is_generated": false, "is_nullable": false, "is_updatable": true, "is_unique": false, "check": null, "enums": [], "comment": null}]}]