I'll help you optimize the data storage for better visualization and ensure we capture all data from the DhanHQ API. Let's modify the schema to include all API fields and add some useful views for visualization:

```ms sql:create_nifty_table.sql
-- First drop dependent views
DROP VIEW IF EXISTS public.latest_options;
DROP VIEW IF EXISTS public.option_chain_view;
DROP VIEW IF EXISTS public.historical_iv_view;
DROP VIEW IF EXISTS public.oi_analysis_view;

-- Drop existing table
DROP TABLE IF EXISTS public.nifty;

-- Create the nifty table with all DhanHQ API fields
CREATE TABLE public.nifty (
    id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    timestamp TIMESTAMP WITH TIME ZONE,
    expiry_date TEXT,
    strike_price NUMERIC(10, 2),
    option_type TEXT CHECK (option_type IN ('CE', 'PE')),
    spot_price NUMERIC(10, 2),
    
    -- Price related fields
    last_price NUMERIC(10, 2),
    previous_close_price NUMERIC(10, 2),
    top_bid_price NUMERIC(10, 2),
    top_ask_price NUMERIC(10, 2),
    
    -- Volume and OI fields
    volume INTEGER,
    previous_volume INTEGER,
    oi INTEGER,
    previous_oi INTEGER,
    top_bid_quantity INTEGER,
    top_ask_quantity INTEGER,
    
    -- Greeks and IV
    iv NUMERIC(10, 4),
    delta NUMERIC(10, 4),
    gamma NUMERIC(10, 6),
    theta NUMERIC(10, 4),
    vega NUMERIC(10, 4),
    
    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now())
);

-- Create indexes for better query performance
CREATE INDEX idx_nifty_expiry_date ON public.nifty(expiry_date);
CREATE INDEX idx_nifty_timestamp ON public.nifty(timestamp);
CREATE INDEX idx_nifty_strike_price ON public.nifty(strike_price);
CREATE INDEX idx_nifty_option_type ON public.nifty(option_type);
CREATE INDEX idx_nifty_composite ON public.nifty(expiry_date, strike_price, option_type);

-- Enable Row Level Security (RLS)
ALTER TABLE public.nifty ENABLE ROW LEVEL SECURITY;

-- Create policies for access control
DO $$ 
BEGIN
    DROP POLICY IF EXISTS "Enable read access for all users" ON public.nifty;
    DROP POLICY IF EXISTS "Enable insert access for authenticated users" ON public.nifty;
EXCEPTION WHEN undefined_object THEN 
    NULL;
END $$;

CREATE POLICY "Enable read access for all users" ON public.nifty
    FOR SELECT USING (true);

CREATE POLICY "Enable insert access for authenticated users" ON public.nifty
    FOR INSERT WITH CHECK (true);

-- Create view for latest options chain data (useful for option chain visualization)
CREATE VIEW public.option_chain_view AS
WITH latest_data AS (
    SELECT DISTINCT ON (strike_price, option_type, expiry_date)
        *
    FROM public.nifty
    ORDER BY strike_price, option_type, expiry_date, timestamp DESC
)
SELECT 
    expiry_date,
    strike_price,
    spot_price,
    MAX(CASE WHEN option_type = 'CE' THEN last_price END) as call_price,
    MAX(CASE WHEN option_type = 'PE' THEN last_price END) as put_price,
    MAX(CASE WHEN option_type = 'CE' THEN oi END) as call_oi,
    MAX(CASE WHEN option_type = 'PE' THEN oi END) as put_oi,
    MAX(CASE WHEN option_type = 'CE' THEN volume END) as call_volume,
    MAX(CASE WHEN option_type = 'PE' THEN volume END) as put_volume,
    MAX(CASE WHEN option_type = 'CE' THEN iv END) as call_iv,
    MAX(CASE WHEN option_type = 'PE' THEN iv END) as put_iv
FROM latest_data
GROUP BY expiry_date, strike_price, spot_price
ORDER BY strike_price;

-- Create view for IV analysis
CREATE VIEW public.historical_iv_view AS
SELECT 
    date_trunc('hour', timestamp) as time_bucket,
    expiry_date,
    strike_price,
    option_type,
    AVG(iv) as avg_iv,
    MAX(iv) as max_iv,
    MIN(iv) as min_iv
FROM public.nifty
GROUP BY date_trunc('hour', timestamp), expiry_date, strike_price, option_type
ORDER BY time_bucket DESC;

-- Create view for OI analysis
CREATE VIEW public.oi_analysis_view AS
WITH ranked_data AS (
    SELECT 
        timestamp,
        expiry_date,
        strike_price,
        option_type,
        oi,
        previous_oi,
        oi - previous_oi as oi_change,
        ROW_NUMBER() OVER (PARTITION BY expiry_date, strike_price, option_type ORDER BY timestamp DESC) as rn
    FROM public.nifty
)
SELECT 
    timestamp,
    expiry_date,
    strike_price,
    option_type,
    oi,
    previous_oi,
    oi_change
FROM ranked_data
WHERE rn = 1
ORDER BY strike_price;

-- Function to get ATM options data
CREATE OR REPLACE FUNCTION public.get_atm_options(target_expiry TEXT)
RETURNS TABLE (
    strike_price NUMERIC,
    call_price NUMERIC,
    put_price NUMERIC,
    call_iv NUMERIC,
    put_iv NUMERIC,
    spot_price NUMERIC
) AS $$
BEGIN
    RETURN QUERY
    WITH latest_data AS (
        SELECT DISTINCT ON (strike_price, option_type)
            *
        FROM public.nifty
        WHERE expiry_date = target_expiry
        ORDER BY strike_price, option_type, timestamp DESC
    ),
    atm_strike AS (
        SELECT strike_price, ABS(strike_price - spot_price) as diff
        FROM latest_data
        GROUP BY strike_price, spot_price
        ORDER BY diff ASC
        LIMIT 1
    )
    SELECT 
        ld.strike_price,
        MAX(CASE WHEN ld.option_type = 'CE' THEN ld.last_price END) as call_price,
        MAX(CASE WHEN ld.option_type = 'PE' THEN ld.last_price END) as put_price,
        MAX(CASE WHEN ld.option_type = 'CE' THEN ld.iv END) as call_iv,
        MAX(CASE WHEN ld.option_type = 'PE' THEN ld.iv END) as put_iv,
        MAX(ld.spot_price) as spot_price
    FROM latest_data ld
    JOIN atm_strike atm ON ld.strike_price = atm.strike_price
    GROUP BY ld.strike_price;
END;
$$ LANGUAGE plpgsql;

-- Function to clean old data
CREATE OR REPLACE FUNCTION public.clean_old_options_data(days_to_keep INTEGER)
RETURNS INTEGER
LANGUAGE plpgsql
AS $$
DECLARE
    rows_deleted INTEGER;
BEGIN
    DELETE FROM public.nifty
    WHERE timestamp < (NOW() - (days_to_keep || ' days')::INTERVAL);
    GET DIAGNOSTICS rows_deleted = ROW_COUNT;
    RETURN rows_deleted;
END;
$$;

-- Add comments
COMMENT ON TABLE public.nifty IS 'Stores complete Nifty options chain data from DhanHQ API';
COMMENT ON VIEW public.option_chain_view IS 'Provides paired call/put data for option chain visualization';
COMMENT ON VIEW public.historical_iv_view IS 'Historical implied volatility analysis by hour';
COMMENT ON VIEW public.oi_analysis_view IS 'Open Interest analysis with change tracking';
COMMENT ON FUNCTION public.get_atm_options IS 'Returns ATM options data for a given expiry';
```
I've made comprehensive changes to optimize the schema for visualization and to store all DhanHQ API data. Here are the key changes:

1. **Enhanced Table Structure**:
   - Added all DhanHQ API fields (previous prices, bid/ask data)
   - Better organized columns into logical groups
   - Added composite index for common query patterns

2. **Visualization-Friendly Views**:

   a. `option_chain_view`: Perfect for option chain visualization
   ```sql
   SELECT expiry_date, strike_price, call_price, put_price, call_oi, put_oi, call_iv, put_iv...
   ```
   Use this for:
   - Option chain tables
   - Strike price vs Premium charts
   - OI distribution charts

   b. `historical_iv_view`: For volatility analysis
   ```sql
   SELECT time_bucket, expiry_date, strike_price, avg_iv, max_iv, min_iv...
   ```
   Use this for:
   - IV smile charts
   - IV surface plots
   - Historical IV trends

   c. `oi_analysis_view`: For OI analysis
   ```sql
   SELECT strike_price, oi, previous_oi, oi_change...
   ```
   Use this for:
   - OI buildup charts
   - PCR ratio analysis
   - OI change heatmaps

3. **Useful Functions**:
   - `get_atm_options(expiry)`: Gets ATM option data for a specific expiry
   - `clean_old_options_data(days)`: Maintains data hygiene

4. **Frontend Visualization Suggestions**:
   - For Option Chain:
     ```javascript
     // Using option_chain_view
     const optionChainData = await supabase
       .from('option_chain_view')
       .select('*')
       .eq('expiry_date', selectedExpiry);
     ```
   
   - For IV Analysis:
     ```javascript
     // Using historical_iv_view
     const ivData = await supabase
       .from('historical_iv_view')
       .select('*')
       .eq('expiry_date', selectedExpiry)
       .order('time_bucket', { ascending: false });
     ```

   - For OI Analysis:
     ```javascript
     // Using oi_analysis_view
     const oiData = await supabase
       .from('oi_analysis_view')
       .select('*')
       .eq('expiry_date', selectedExpiry);
     ```

5. **Recommended Visualization Libraries**:
   - Charts: `recharts` or `chart.js`
   - 3D Surfaces: `plotly.js`
   - Tables: `ag-grid` or `material-table`
   - Heatmaps: `visx` or `nivo`

Would you like me to:
1. Add more specific views for particular types of analysis?
2. Create additional helper functions for common calculations?
3. Provide example queries for specific visualization scenarios?
4. Add more sophisticated data aggregation views?
