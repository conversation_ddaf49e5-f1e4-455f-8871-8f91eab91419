"""
Admin API Module

This module provides administrative functions for managing option chain data collection,
including asset management, settings configuration, and monitoring.
"""

import os
import json
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional, Union

from supabase import create_client, Client
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Initialize logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Initialize Supabase client
supabase: Client = create_client(
    os.getenv('SUPABASE_URL'),
    os.getenv('SUPABASE_KEY')
)

class AdminAPI:
    """Admin API for managing option chain data collection"""

    @staticmethod
    def get_assets(active_only: bool = False) -> List[Dict[str, Any]]:
        """
        Get all assets configured in the system
        
        Args:
            active_only: If True, only return active assets
            
        Returns:
            List of asset records
        """
        try:
            query = supabase.table('option_assets').select('*')
            
            if active_only:
                query = query.eq('is_active', True)
                
            query = query.order('priority', desc=False)
            response = query.execute()
            
            if response.data:
                return response.data
            return []
        except Exception as e:
            logger.error(f"Error fetching assets: {e}")
            return []

    @staticmethod
    def get_asset(asset_code: str) -> Optional[Dict[str, Any]]:
        """
        Get a specific asset by code
        
        Args:
            asset_code: The asset code to retrieve
            
        Returns:
            Asset record or None if not found
        """
        try:
            response = supabase.table('option_assets')\
                .select('*')\
                .eq('asset_code', asset_code)\
                .execute()
            
            if response.data and len(response.data) > 0:
                return response.data[0]
            return None
        except Exception as e:
            logger.error(f"Error fetching asset {asset_code}: {e}")
            return None

    @staticmethod
    def create_asset(asset_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a new asset
        
        Args:
            asset_data: Dictionary containing asset details
            
        Returns:
            Created asset record or error message
        """
        try:
            # Validate required fields
            required_fields = ['asset_code', 'security_id', 'exchange_segment', 
                               'lot_size', 'name', 'priority']
            
            for field in required_fields:
                if field not in asset_data:
                    return {'error': f"Missing required field: {field}"}
            
            # Insert the new asset
            response = supabase.table('option_assets')\
                .insert(asset_data)\
                .execute()
            
            if response.data and len(response.data) > 0:
                # Create database tables for this asset
                try:
                    rpc_response = supabase.rpc(
                        'admin_create_option_tables',
                        {'asset_code': asset_data['asset_code']}
                    ).execute()
                    logger.info(f"Created tables for asset {asset_data['asset_code']}")
                except Exception as table_error:
                    logger.error(f"Error creating tables for asset {asset_data['asset_code']}: {table_error}")
                
                return response.data[0]
            
            return {'error': "Failed to create asset"}
        except Exception as e:
            logger.error(f"Error creating asset: {e}")
            return {'error': str(e)}

    @staticmethod
    def update_asset(asset_code: str, asset_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Update an existing asset
        
        Args:
            asset_code: The asset code to update
            asset_data: Dictionary containing updated asset details
            
        Returns:
            Updated asset record or error message
        """
        try:
            # Prevent updating the asset_code
            if 'asset_code' in asset_data:
                del asset_data['asset_code']
            
            # Update the asset
            response = supabase.table('option_assets')\
                .update(asset_data)\
                .eq('asset_code', asset_code)\
                .execute()
            
            if response.data and len(response.data) > 0:
                return response.data[0]
            
            return {'error': f"Asset {asset_code} not found"}
        except Exception as e:
            logger.error(f"Error updating asset {asset_code}: {e}")
            return {'error': str(e)}

    @staticmethod
    def delete_asset(asset_code: str) -> Dict[str, Any]:
        """
        Delete an asset (mark as inactive)
        
        Args:
            asset_code: The asset code to delete
            
        Returns:
            Success message or error message
        """
        try:
            # Instead of deleting, mark as inactive
            response = supabase.table('option_assets')\
                .update({'is_active': False})\
                .eq('asset_code', asset_code)\
                .execute()
            
            if response.data and len(response.data) > 0:
                return {'success': f"Asset {asset_code} marked as inactive"}
            
            return {'error': f"Asset {asset_code} not found"}
        except Exception as e:
            logger.error(f"Error deactivating asset {asset_code}: {e}")
            return {'error': str(e)}

    @staticmethod
    def get_settings(setting_key: Optional[str] = None) -> Union[Dict[str, Any], List[Dict[str, Any]]]:
        """
        Get application settings
        
        Args:
            setting_key: Optional specific setting key to retrieve
            
        Returns:
            Dictionary of settings or list of setting records
        """
        try:
            if setting_key:
                response = supabase.table('app_settings')\
                    .select('*')\
                    .eq('setting_key', setting_key)\
                    .execute()
                
                if response.data and len(response.data) > 0:
                    return response.data[0]
                return {'error': f"Setting {setting_key} not found"}
            else:
                response = supabase.table('app_settings')\
                    .select('*')\
                    .execute()
                
                if response.data:
                    return response.data
                return []
        except Exception as e:
            logger.error(f"Error fetching settings: {e}")
            return {'error': str(e)}

    @staticmethod
    def update_settings(setting_key: str, setting_value: Dict[str, Any]) -> Dict[str, Any]:
        """
        Update application settings
        
        Args:
            setting_key: The setting key to update
            setting_value: New setting value (JSONB)
            
        Returns:
            Updated setting record or error message
        """
        try:
            # Get current setting
            current = AdminAPI.get_settings(setting_key)
            
            if isinstance(current, dict) and 'error' in current:
                return current
            
            # Update the setting
            response = supabase.table('app_settings')\
                .update({'setting_value': setting_value})\
                .eq('setting_key', setting_key)\
                .execute()
            
            if response.data and len(response.data) > 0:
                return response.data[0]
            
            return {'error': f"Failed to update setting {setting_key}"}
        except Exception as e:
            logger.error(f"Error updating setting {setting_key}: {e}")
            return {'error': str(e)}

    @staticmethod
    def get_scheduled_tasks(asset_code: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Get scheduled tasks
        
        Args:
            asset_code: Optional asset code to filter tasks
            
        Returns:
            List of scheduled task records
        """
        try:
            query = supabase.table('scheduled_tasks').select('*')
            
            if asset_code:
                query = query.eq('asset_code', asset_code)
                
            response = query.execute()
            
            if response.data:
                return response.data
            return []
        except Exception as e:
            logger.error(f"Error fetching scheduled tasks: {e}")
            return []

    @staticmethod
    def update_task(task_id: int, task_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Update a scheduled task
        
        Args:
            task_id: The ID of the task to update
            task_data: Updated task data
            
        Returns:
            Updated task record or error message
        """
        try:
            response = supabase.table('scheduled_tasks')\
                .update(task_data)\
                .eq('id', task_id)\
                .execute()
            
            if response.data and len(response.data) > 0:
                return response.data[0]
            
            return {'error': f"Task with ID {task_id} not found"}
        except Exception as e:
            logger.error(f"Error updating task {task_id}: {e}")
            return {'error': str(e)}

    @staticmethod
    def get_audit_logs(limit: int = 100) -> List[Dict[str, Any]]:
        """
        Get audit logs
        
        Args:
            limit: Maximum number of logs to retrieve
            
        Returns:
            List of audit log records
        """
        try:
            response = supabase.table('admin_audit_log')\
                .select('*')\
                .order('created_at', desc=True)\
                .limit(limit)\
                .execute()
            
            if response.data:
                return response.data
            return []
        except Exception as e:
            logger.error(f"Error fetching audit logs: {e}")
            return []

    @staticmethod
    def clean_old_data(asset_code: str, days_to_keep: int = 30) -> Dict[str, Any]:
        """
        Clean old data for a specific asset
        
        Args:
            asset_code: Asset code to clean data for
            days_to_keep: Number of days of data to keep
            
        Returns:
            Success message with number of deleted records or error message
        """
        try:
            function_name = f"clean_old_{asset_code.lower()}_data"
            
            response = supabase.rpc(
                function_name,
                {'days_to_keep': days_to_keep}
            ).execute()
            
            if hasattr(response, 'data'):
                rows_deleted = response.data
                return {'success': f"Deleted {rows_deleted} records older than {days_to_keep} days for {asset_code}"}
            
            return {'error': f"Failed to clean data for {asset_code}"}
        except Exception as e:
            logger.error(f"Error cleaning data for {asset_code}: {e}")
            return {'error': str(e)}

    @staticmethod
    def initialize_from_config():
        """Initialize database from configuration file"""
        try:
            # Import config file 
            from option_chain_config import OPTION_CHAIN_ASSETS, OPTION_CHAIN_SETTINGS, ADMIN_SETTINGS, DATABASE_CONFIG
            
            # Insert assets if they don't exist
            for asset_code, asset_data in OPTION_CHAIN_ASSETS.items():
                existing_asset = AdminAPI.get_asset(asset_code)
                
                if not existing_asset:
                    # Prepare asset data
                    asset_record = {
                        'asset_code': asset_code,
                        'security_id': asset_data.get('security_id'),
                        'exchange_segment': asset_data.get('exchange_segment'),
                        'lot_size': asset_data.get('lot_size'),
                        'name': asset_data.get('name'),
                        'description': asset_data.get('description'),
                        'is_active': asset_data.get('is_active', True),
                        'priority': asset_data.get('priority', 999)
                    }
                    
                    AdminAPI.create_asset(asset_record)
                    logger.info(f"Created asset: {asset_code}")
                
            # Update settings
            AdminAPI.update_settings('option_chain_settings', OPTION_CHAIN_SETTINGS)
            AdminAPI.update_settings('admin_settings', ADMIN_SETTINGS)
            AdminAPI.update_settings('database_config', DATABASE_CONFIG)
            
            logger.info("Successfully initialized database from configuration")
            return {'success': "Database initialized from configuration"}
        except Exception as e:
            logger.error(f"Error initializing database from configuration: {e}")
            return {'error': str(e)}


# Example usage if run directly
if __name__ == "__main__":
    # Initialize database from configuration
    result = AdminAPI.initialize_from_config()
    print(result)
    
    # Get all assets
    assets = AdminAPI.get_assets()
    print(f"Found {len(assets)} assets:")
    for asset in assets:
        print(f"  - {asset['asset_code']}: {asset['name']} (Active: {asset['is_active']})") 