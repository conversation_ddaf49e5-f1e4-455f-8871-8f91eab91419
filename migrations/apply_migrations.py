import os
from pathlib import Path
import asyncio
from dotenv import load_dotenv
import json

# Load environment variables
load_dotenv()

async def apply_migrations():
    """Apply all migrations to the Supabase database"""
    try:
        # Get the directory containing the migration files
        migrations_dir = Path(__file__).parent
        
        # Read migration files in order
        migration_files = [
            "01_create_base_table.sql",
            "02_create_continuous_aggregates.sql",
            "03_create_refresh_policies.sql",
            "04_create_materialized_data.sql"
        ]

        # Read and combine all migration files
        combined_migration = ""
        for file_name in migration_files:
            file_path = migrations_dir / file_name
            print(f"Reading migration file: {file_name}")
            
            if not file_path.exists():
                raise FileNotFoundError(f"Migration file not found: {file_name}")
            
            with open(file_path, 'r') as f:
                content = f.read()
                combined_migration += f"\n-- Migration: {file_name}\n"
                combined_migration += content
                combined_migration += "\n"

        # Apply the migration
        print("Applying migrations...")
        
        # Create a migration name based on timestamp
        from datetime import datetime
        migration_name = f"timescaledb_setup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        # Format the tool call as a JSON string
        tool_call = {
            "name": "mcp_supabase_apply_migration",
            "parameters": {
                "project_id": os.getenv('SUPABASE_PROJECT_ID'),
                "name": migration_name,
                "query": combined_migration
            }
        }
        
        # Print the tool call for execution
        print("\nTool call for migration:")
        print(json.dumps(tool_call, indent=2))
        
        print("\nPlease execute this tool call to apply the migrations.")
        return tool_call

    except Exception as e:
        print(f"Error preparing migrations: {str(e)}")
        raise

if __name__ == "__main__":
    asyncio.run(apply_migrations()) 