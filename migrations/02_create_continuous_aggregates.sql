-- Create a continuous aggregate for 5-minute OHLCV data
CREATE MATERIALIZED VIEW IF NOT EXISTS nifty_5min_ohlcv
WITH (timescaledb.continuous) AS
SELECT 
    time_bucket('5 minutes', timestamp) AS bucket,
    expiry_date,
    strike_price,
    option_type,
    FIRST(last_price, timestamp) as open,
    MAX(last_price) as high,
    MIN(last_price) as low,
    LAST(last_price, timestamp) as close,
    SUM(volume) as volume,
    LAST(oi, timestamp) as oi,
    LAST(spot_price, timestamp) as spot_price,
    AVG(iv) as avg_iv,
    AVG(delta) as avg_delta,
    AVG(theta) as avg_theta
FROM nifty_timeseries
GROUP BY bucket, expiry_date, strike_price, option_type
WITH NO DATA;

-- Create a continuous aggregate for daily statistics
CREATE MATERIALIZED VIEW IF NOT EXISTS nifty_daily_stats
WITH (timescaledb.continuous) AS
SELECT 
    time_bucket('1 day', timestamp) AS day,
    expiry_date,
    strike_price,
    option_type,
    FIRST(last_price, timestamp) as open_price,
    MAX(last_price) as high_price,
    MIN(last_price) as low_price,
    LAST(last_price, timestamp) as close_price,
    LAST(spot_price, timestamp) as spot_price,
    AVG(iv) as avg_iv,
    AVG(delta) as avg_delta,
    MAX(oi) as max_oi,
    MIN(oi) as min_oi,
    SUM(volume) as total_volume,
    COUNT(*) as update_count
FROM nifty_timeseries
GROUP BY day, expiry_date, strike_price, option_type
WITH NO DATA; 