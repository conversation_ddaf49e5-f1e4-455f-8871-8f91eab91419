-- Enable TimescaleDB extension (if not already enabled)
CREATE EXTENSION IF NOT EXISTS timescaledb;

-- First, let's create a new table with proper timestamp column
CREATE TABLE IF NOT EXISTS nifty_timeseries (
    timestamp TIMESTAMPTZ NOT NULL,
    expiry_date DATE NOT NULL,
    strike_price NUMERIC(10,2) NOT NULL,
    spot_price NUMERIC(10,2) NOT NULL,
    option_type VARCHAR(2) NOT NULL,
    last_price NUMERIC(10,2) NOT NULL,
    oi INTEGER NOT NULL,
    volume INTEGER NOT NULL,
    iv NUMERIC(10,4) NOT NULL,
    delta NUMERIC(10,4) NOT NULL,
    theta NUMERIC(10,4) NOT NULL,
    gamma NUMERIC(10,4),
    vega NUMERIC(10,4),
    -- Create a composite primary key that includes the timestamp
    PRIMARY KEY (timestamp, expiry_date, strike_price, option_type)
);

-- Create hypertable
SELECT create_hypertable('nifty_timeseries', 'timestamp', 
    chunk_time_interval => INTERVAL '1 day',
    if_not_exists => TRUE
);

-- <PERSON><PERSON> indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_nifty_timeseries_expiry_timestamp 
    ON nifty_timeseries (expiry_date, timestamp DESC);

CREATE INDEX IF NOT EXISTS idx_nifty_timeseries_strike_timestamp 
    ON nifty_timeseries (strike_price, timestamp DESC);

CREATE INDEX IF NOT EXISTS idx_nifty_timeseries_option_type_timestamp 
    ON nifty_timeseries (option_type, timestamp DESC);

-- Create a continuous aggregate for 5-minute OHLCV data
CREATE MATERIALIZED VIEW nifty_5min_ohlcv
WITH (timescaledb.continuous) AS
SELECT 
    time_bucket('5 minutes', timestamp) AS bucket,
    expiry_date,
    strike_price,
    option_type,
    FIRST(last_price, timestamp) as open,
    MAX(last_price) as high,
    MIN(last_price) as low,
    LAST(last_price, timestamp) as close,
    SUM(volume) as volume,
    LAST(oi, timestamp) as oi
FROM nifty_timeseries
GROUP BY bucket, expiry_date, strike_price, option_type;

-- Add refresh policy for continuous aggregate
SELECT add_continuous_aggregate_policy('nifty_5min_ohlcv',
    start_offset => INTERVAL '1 day',
    end_offset => INTERVAL '5 minutes',
    schedule_interval => INTERVAL '5 minutes');

-- Create a continuous aggregate for daily statistics
CREATE MATERIALIZED VIEW nifty_daily_stats
WITH (timescaledb.continuous) AS
SELECT 
    time_bucket('1 day', timestamp) AS day,
    expiry_date,
    strike_price,
    option_type,
    AVG(iv) as avg_iv,
    AVG(delta) as avg_delta,
    MAX(oi) as max_oi,
    MIN(oi) as min_oi,
    MAX(last_price) as high_price,
    MIN(last_price) as low_price,
    LAST(last_price, timestamp) as close_price
FROM nifty_timeseries
GROUP BY day, expiry_date, strike_price, option_type;

-- Add refresh policy for daily stats
SELECT add_continuous_aggregate_policy('nifty_daily_stats',
    start_offset => INTERVAL '1 month',
    end_offset => INTERVAL '1 day',
    schedule_interval => INTERVAL '1 day');

-- Create functions for common time-series operations
CREATE OR REPLACE FUNCTION get_price_movement(
    p_expiry_date DATE,
    p_strike_price NUMERIC,
    p_option_type VARCHAR,
    p_interval INTERVAL
)
RETURNS TABLE (
    bucket TIMESTAMPTZ,
    open NUMERIC,
    high NUMERIC,
    low NUMERIC,
    close NUMERIC,
    volume BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        time_bucket(p_interval, timestamp) AS bucket,
        FIRST(last_price, timestamp) as open,
        MAX(last_price) as high,
        MIN(last_price) as low,
        LAST(last_price, timestamp) as close,
        SUM(volume) as volume
    FROM nifty_timeseries
    WHERE expiry_date = p_expiry_date
        AND strike_price = p_strike_price
        AND option_type = p_option_type
    GROUP BY bucket
    ORDER BY bucket;
END;
$$ LANGUAGE plpgsql;

-- Function to get IV term structure
CREATE OR REPLACE FUNCTION get_iv_term_structure(
    p_timestamp TIMESTAMPTZ
)
RETURNS TABLE (
    expiry_date DATE,
    avg_call_iv NUMERIC,
    avg_put_iv NUMERIC
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        nt.expiry_date,
        AVG(CASE WHEN option_type = 'CE' THEN iv END) as avg_call_iv,
        AVG(CASE WHEN option_type = 'PE' THEN iv END) as avg_put_iv
    FROM nifty_timeseries nt
    WHERE timestamp = (
        SELECT MAX(timestamp)
        FROM nifty_timeseries
        WHERE timestamp <= p_timestamp
    )
    GROUP BY expiry_date
    ORDER BY expiry_date;
END;
$$ LANGUAGE plpgsql; 