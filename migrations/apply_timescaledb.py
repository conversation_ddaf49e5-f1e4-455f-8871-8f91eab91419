import os
from pathlib import Path
import asyncio
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

async def verify_project():
    """Verify the Supabase project exists and is accessible"""
    from mcp_supabase_get_project import get_project
    
    project_id = os.getenv('SUPABASE_PROJECT_ID')
    if not project_id:
        raise ValueError("SUPABASE_PROJECT_ID not found in environment variables")
        
    result = await get_project(id=project_id)
    print(f"Connected to project: {result['name']}")
    return result

async def apply_migration(name: str, sql_content: str):
    """Apply a single migration"""
    from mcp_supabase_apply_migration import apply_migration
    
    project_id = os.getenv('SUPABASE_PROJECT_ID')
    result = await apply_migration(
        project_id=project_id,
        name=name,
        query=sql_content
    )
    return result

async def verify_tables():
    """Verify that tables were created successfully"""
    from mcp_supabase_list_tables import list_tables
    
    project_id = os.getenv('SUPABASE_PROJECT_ID')
    tables = await list_tables(project_id=project_id)
    
    required_tables = ['nifty_timeseries', 'nifty_5min_ohlcv', 'nifty_daily_stats']
    missing_tables = [table for table in required_tables if table not in [t['name'] for t in tables]]
    
    if missing_tables:
        raise ValueError(f"Missing tables after migration: {', '.join(missing_tables)}")
    
    print("All required tables created successfully!")
    return tables

async def apply_migrations():
    """Apply all migrations to the Supabase database"""
    try:
        # Verify project access
        await verify_project()
        
        # Get the directory containing the migration files
        migrations_dir = Path(__file__).parent
        
        # Migration files and their descriptions
        migrations = [
            ("01_create_base_table.sql", "Creating base table and hypertable"),
            ("02_create_continuous_aggregates.sql", "Creating continuous aggregates"),
            ("03_create_refresh_policies.sql", "Setting up refresh policies"),
            ("04_create_materialized_data.sql", "Refreshing materialized views")
        ]

        # Apply each migration
        for file_name, description in migrations:
            file_path = migrations_dir / file_name
            print(f"\nApplying migration: {description}")
            
            if not file_path.exists():
                raise FileNotFoundError(f"Migration file not found: {file_name}")
            
            with open(file_path, 'r') as f:
                content = f.read()
                
            # Create a unique name for this migration
            from datetime import datetime
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            migration_name = f"timescaledb_{file_name.split('.')[0]}_{timestamp}"
            
            # Apply the migration
            result = await apply_migration(migration_name, content)
            print(f"Migration applied: {migration_name}")
        
        # Verify tables were created
        await verify_tables()
        
        print("\nAll migrations completed successfully!")

    except Exception as e:
        print(f"\nError applying migrations: {str(e)}")
        raise

if __name__ == "__main__":
    asyncio.run(apply_migrations()) 