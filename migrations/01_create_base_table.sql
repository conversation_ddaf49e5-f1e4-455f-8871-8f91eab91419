-- Enable TimescaleDB extension (if not already enabled)
CREATE EXTENSION IF NOT EXISTS timescaledb;

-- First, let's create a new table with proper timestamp column
CREATE TABLE IF NOT EXISTS nifty_timeseries (
    timestamp TIMESTAMPTZ NOT NULL,
    expiry_date DATE NOT NULL,
    strike_price NUMERIC(10,2) NOT NULL,
    spot_price NUMERIC(10,2) NOT NULL,
    option_type VARCHAR(2) NOT NULL,
    last_price NUMERIC(10,2) NOT NULL,
    oi INTEGER NOT NULL,
    volume INTEGER NOT NULL,
    iv NUMERIC(10,4) NOT NULL,
    delta NUMERIC(10,4) NOT NULL,
    theta NUMERIC(10,4) NOT NULL,
    gamma NUMERIC(10,4),
    vega NUMERIC(10,4),
    -- Create a composite primary key that includes the timestamp
    PRIMARY KEY (timestamp, expiry_date, strike_price, option_type)
);

-- Create hypertable
SELECT create_hypertable('nifty_timeseries', 'timestamp', 
    chunk_time_interval => INTERVAL '1 day',
    if_not_exists => TRUE
);

-- <PERSON><PERSON> indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_nifty_timeseries_expiry_timestamp 
    ON nifty_timeseries (expiry_date, timestamp DESC);

CREATE INDEX IF NOT EXISTS idx_nifty_timeseries_strike_timestamp 
    ON nifty_timeseries (strike_price, timestamp DESC);

CREATE INDEX IF NOT EXISTS idx_nifty_timeseries_option_type_timestamp 
    ON nifty_timeseries (option_type, timestamp DESC);

-- Add compression policy (compress chunks older than 7 days)
SELECT add_compression_policy('nifty_timeseries', INTERVAL '7 days');

-- Create a function to validate option data before insertion
CREATE OR REPLACE FUNCTION validate_option_data()
RETURNS TRIGGER AS $$
BEGIN
    -- Validate option type
    IF NEW.option_type NOT IN ('CE', 'PE') THEN
        RAISE EXCEPTION 'Invalid option_type: %. Must be CE or PE', NEW.option_type;
    END IF;

    -- Validate numeric ranges
    IF NEW.strike_price <= 0 THEN
        RAISE EXCEPTION 'Invalid strike_price: %. Must be positive', NEW.strike_price;
    END IF;

    IF NEW.spot_price <= 0 THEN
        RAISE EXCEPTION 'Invalid spot_price: %. Must be positive', NEW.spot_price;
    END IF;

    IF NEW.last_price < 0 THEN
        RAISE EXCEPTION 'Invalid last_price: %. Must be non-negative', NEW.last_price;
    END IF;

    IF NEW.oi < 0 THEN
        RAISE EXCEPTION 'Invalid oi: %. Must be non-negative', NEW.oi;
    END IF;

    IF NEW.volume < 0 THEN
        RAISE EXCEPTION 'Invalid volume: %. Must be non-negative', NEW.volume;
    END IF;

    -- Validate IV range (typically between 0 and 5 or 500%)
    IF NEW.iv < 0 OR NEW.iv > 5 THEN
        RAISE EXCEPTION 'Invalid iv: %. Must be between 0 and 5', NEW.iv;
    END IF;

    -- Validate delta range (-1 to 1)
    IF NEW.delta < -1 OR NEW.delta > 1 THEN
        RAISE EXCEPTION 'Invalid delta: %. Must be between -1 and 1', NEW.delta;
    END IF;

    -- Validate expiry date is in the future at insertion time
    IF NEW.expiry_date < CURRENT_DATE THEN
        RAISE EXCEPTION 'Invalid expiry_date: %. Must be a future date', NEW.expiry_date;
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for data validation
CREATE TRIGGER validate_option_data_trigger
    BEFORE INSERT OR UPDATE ON nifty_timeseries
    FOR EACH ROW
    EXECUTE FUNCTION validate_option_data(); 