#!/bin/bash

# Exit on error
set -e

# Get the directory of the script
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# Load environment variables
if [ -f ../.env ]; then
    source ../.env
else
    echo "Error: .env file not found in parent directory"
    exit 1
fi

# Check if required environment variables are set
required_vars=("SUPABASE_URL_FULL" "SUPABASE_KEY")
for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ]; then
        echo "Error: $var is not set in .env file"
        exit 1
    fi
done

# Extract database credentials from SUPABASE_URL_FULL
# Format: postgres://postgres:[PASSWORD]@[HOST]:[PORT]/postgres
if [[ $SUPABASE_URL_FULL =~ postgres://([^:]+):([^@]+)@([^:]+):([^/]+)/(.+) ]]; then
    DB_USER="${BASH_REMATCH[1]}"
    DB_PASSWORD="${BASH_REMATCH[2]}"
    DB_HOST="${BASH_REMATCH[3]}"
    DB_PORT="${BASH_REMATCH[4]}"
    DB_NAME="${BASH_REMATCH[5]}"

    # Debug info (comment out in production)
    echo "Database connection details:"
    echo "Host: $DB_HOST"
    echo "Port: $DB_PORT"
    echo "User: $DB_USER"
    echo "Database: $DB_NAME"
else
    echo "Error: Invalid SUPABASE_URL_FULL format"
    echo "Expected format: postgres://USER:PASSWORD@HOST:PORT/DATABASE"
    echo "Got: $SUPABASE_URL_FULL"
    exit 1
fi

# Check if psql is installed
if ! command -v psql &>/dev/null; then
    echo "Error: psql is not installed. Please install PostgreSQL command-line tools first."
    exit 1
fi

# Check if all required SQL files exist
required_files=("01_create_base_table.sql" "02_create_continuous_aggregates.sql" "03_create_refresh_policies.sql" "04_create_materialized_data.sql")
for file in "${required_files[@]}"; do
    if [ ! -f "$file" ]; then
        echo "Error: Required SQL file $file not found"
        exit 1
    fi
done

# Test database connection
echo "Testing database connection..."
if ! PGPASSWORD="${DB_PASSWORD}" psql \
    -h "${DB_HOST}" \
    -p "${DB_PORT}" \
    -U "${DB_USER}" \
    -d "${DB_NAME}" \
    -c "SELECT version();" >/dev/null 2>&1; then
    echo "Error: Could not connect to database. Please check your credentials and network connection."
    exit 1
fi

echo "Starting migrations..."

# Function to run migration file
run_migration() {
    local file=$1
    local description=$2
    echo "Running $file ($description)..."

    # Run the migration using psql directly (since we're dealing with TimescaleDB specifics)
    PGPASSWORD="${DB_PASSWORD}" psql \
        -h "${DB_HOST}" \
        -p "${DB_PORT}" \
        -U "${DB_USER}" \
        -d "${DB_NAME}" \
        -f "$file" \
        -v ON_ERROR_STOP=1

    local status=$?
    if [ $status -ne 0 ]; then
        echo "Error running $file (exit code: $status)"
        exit $status
    fi
}

# Function to run migration file without transaction
run_migration_no_transaction() {
    local file=$1
    local description=$2
    echo "Running $file ($description)..."

    # Run the migration using psql directly without transaction
    PGPASSWORD="${DB_PASSWORD}" psql \
        -h "${DB_HOST}" \
        -p "${DB_PORT}" \
        -U "${DB_USER}" \
        -d "${DB_NAME}" \
        -f "$file" \
        -v ON_ERROR_STOP=1 \
        --single-transaction

    local status=$?
    if [ $status -ne 0 ]; then
        echo "Error running $file (exit code: $status)"
        exit $status
    fi
}

echo "1. Creating base table..."
run_migration "01_create_base_table.sql" "Base table setup"

echo "2. Creating continuous aggregates..."
run_migration_no_transaction "02_create_continuous_aggregates.sql" "Continuous aggregates"

echo "3. Setting up refresh policies..."
run_migration "03_create_refresh_policies.sql" "Refresh policies"

echo "4. Refreshing materialized views..."
run_migration_no_transaction "04_create_materialized_data.sql" "Data materialization"

echo "Migrations completed successfully!"
