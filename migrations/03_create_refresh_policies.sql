-- Add refresh policy for 5-minute OHLCV
SELECT add_continuous_aggregate_policy('nifty_5min_ohlcv',
    start_offset => INTERVAL '1 day',
    end_offset => INTERVAL '5 minutes',
    schedule_interval => INTERVAL '5 minutes');

-- Add refresh policy for daily stats
SELECT add_continuous_aggregate_policy('nifty_daily_stats',
    start_offset => INTERVAL '1 month',
    end_offset => INTERVAL '1 day',
    schedule_interval => INTERVAL '1 day');

-- Create function to get price movement
CREATE OR REPLACE FUNCTION get_price_movement(
    p_expiry_date DATE,
    p_strike_price NUMERIC,
    p_option_type VARCHAR,
    p_interval INTERVAL DEFAULT INTERVAL '5 minutes',
    p_start_time TIMESTAMPTZ DEFAULT NULL,
    p_end_time TIMESTAMPTZ DEFAULT NULL
)
RETURNS TABLE (
    bucket TIMESTAMPTZ,
    open NUMERIC,
    high NUMERIC,
    low NUMERIC,
    close NUMERIC,
    volume BIGINT,
    oi INTEGER,
    spot_price NUMERIC,
    avg_iv NUMERIC
) AS $$
BEGIN
    -- Use 5-minute view for intervals >= 5 minutes
    IF p_interval >= INTERVAL '5 minutes' THEN
        RETURN QUERY
        SELECT 
            m.bucket,
            m.open,
            m.high,
            m.low,
            m.close,
            m.volume,
            m.oi,
            m.spot_price,
            m.avg_iv
        FROM nifty_5min_ohlcv m
        WHERE m.expiry_date = p_expiry_date
            AND m.strike_price = p_strike_price
            AND m.option_type = p_option_type
            AND (p_start_time IS NULL OR m.bucket >= p_start_time)
            AND (p_end_time IS NULL OR m.bucket <= p_end_time)
        ORDER BY m.bucket;
    ELSE
        -- Use raw data for intervals < 5 minutes
        RETURN QUERY
        SELECT 
            time_bucket(p_interval, timestamp) AS bucket,
            FIRST(last_price, timestamp) as open,
            MAX(last_price) as high,
            MIN(last_price) as low,
            LAST(last_price, timestamp) as close,
            SUM(volume)::bigint as volume,
            LAST(oi, timestamp) as oi,
            LAST(spot_price, timestamp) as spot_price,
            AVG(iv) as avg_iv
        FROM nifty_timeseries
        WHERE expiry_date = p_expiry_date
            AND strike_price = p_strike_price
            AND option_type = p_option_type
            AND (p_start_time IS NULL OR timestamp >= p_start_time)
            AND (p_end_time IS NULL OR timestamp <= p_end_time)
        GROUP BY bucket
        ORDER BY bucket;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Create function to get IV term structure
CREATE OR REPLACE FUNCTION get_iv_term_structure(
    p_timestamp TIMESTAMPTZ DEFAULT NOW(),
    p_strike_offset INTEGER DEFAULT 5  -- Number of strikes above and below ATM
)
RETURNS TABLE (
    expiry_date DATE,
    strike_price NUMERIC,
    call_iv NUMERIC,
    put_iv NUMERIC,
    atm_distance INTEGER
) AS $$
WITH latest_spot AS (
    SELECT DISTINCT ON (timestamp)
        timestamp,
        spot_price
    FROM nifty_timeseries
    WHERE timestamp <= p_timestamp
    ORDER BY timestamp DESC
    LIMIT 1
),
atm_strike AS (
    SELECT 
        strike_price,
        ABS(strike_price - (SELECT spot_price FROM latest_spot)) as distance
    FROM nifty_timeseries
    WHERE timestamp = (SELECT timestamp FROM latest_spot)
    GROUP BY strike_price
    ORDER BY distance
    LIMIT 1
)
SELECT 
    nt.expiry_date,
    nt.strike_price,
    AVG(CASE WHEN nt.option_type = 'CE' THEN nt.iv END) as call_iv,
    AVG(CASE WHEN nt.option_type = 'PE' THEN nt.iv END) as put_iv,
    ROUND((nt.strike_price - (SELECT strike_price FROM atm_strike))/100) as atm_distance
FROM nifty_timeseries nt
WHERE nt.timestamp = (SELECT timestamp FROM latest_spot)
    AND nt.strike_price BETWEEN 
        (SELECT strike_price - (p_strike_offset * 100) FROM atm_strike) 
        AND (SELECT strike_price + (p_strike_offset * 100) FROM atm_strike)
GROUP BY nt.expiry_date, nt.strike_price
ORDER BY nt.expiry_date, nt.strike_price;
$$ LANGUAGE SQL; 