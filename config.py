# Asset Configuration
ASSETS = {
    'NIFTY': {
        'security_id': 13,  # Nifty security ID for DhanHQ
        'exchange_segment': 'IDX_I',
        'lot_size': 50,
        'name': 'NIFTY 50',
        'description': 'National Stock Exchange Nifty 50 Index'
    },
    'BANKNIFTY': {
        'security_id': 11,  # BankNifty security ID for DhanHQ
        'exchange_segment': 'IDX_I',
        'lot_size': 25,
        'name': 'NIFTY BANK',
        'description': 'National Stock Exchange Nifty Bank Index'
    }
}

# Time Configuration
TIME_CONFIG = {
    'refresh_interval_seconds': 3,  # How often to fetch new data
    'rate_limit_seconds': 3,        # Minimum time between API calls
    'market_start_time': '09:15',   # Market opening time (IST)
    'market_end_time': '16:00',     # Market closing time (IST)
    'pre_market_start': '09:00',    # Pre-market data collection start (IST)
    'post_market_end': '16:00'      # Post-market data collection end (IST)
}

# Database Configuration
DB_CONFIG = {
    'batch_size': 1000,             # Number of records to insert in one batch
    'max_retries': 3,               # Maximum number of retries for failed operations
    'cleanup_days': 30              # Number of days to keep historical data
}

# Logging Configuration
LOG_CONFIG = {
    'log_level': 'INFO',            # Logging level (DEBUG, INFO, WARNING, ERROR)
    'log_to_file': True,            # Whether to save logs to file
    'log_file': 'options_data.log', # Log file name
    'max_log_size': 10_000_000,     # Maximum log file size in bytes (10MB)
    'backup_count': 5               # Number of backup log files to keep
}

# Error Handling Configuration
ERROR_CONFIG = {
    'max_consecutive_errors': 5,     # Maximum number of consecutive errors before alerting
    'error_cooldown_seconds': 60,    # Time to wait after max errors before resuming
    'alert_on_error': True          # Whether to send alerts on errors
}

# API Rate Limiting
RATE_LIMIT_CONFIG = {
    'max_requests_per_minute': 60,   # Maximum API requests per minute
    'max_requests_per_hour': 1000,   # Maximum API requests per hour
    'backoff_factor': 2,            # Exponential backoff factor for retries
    'initial_backoff_seconds': 1     # Initial backoff time for retries
}

# Trading Hours Check
def is_trading_hours():
    """Check if current time is within trading hours"""
    from datetime import datetime
    import pytz
    
    # Get current time in IST
    ist = pytz.timezone('Asia/Kolkata')
    current_time = datetime.now(ist).strftime('%H:%M')
    
    # Check if within trading hours
    return TIME_CONFIG['market_start_time'] <= current_time <= TIME_CONFIG['market_end_time']

# Default Asset
DEFAULT_ASSET = 'NIFTY'  # Default asset to track if none specified 