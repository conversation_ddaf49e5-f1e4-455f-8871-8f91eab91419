# Options Data Manager

## Quick Start

```bash
# Set up virtual environment 
python -m venv .venv
# Activate and install dependencies
.venv/bin/activate && uv pip install -r requirements.txt

# Set up database schema
python setup_db_schema.py

# Run the options data collector
python options_manager.py
```

A service to fetch and store options chain data from DhanHQ into a Supabase PostgreSQL database. Now with support for multiple assets and admin controls.

## Features

- Authenticates with DhanHQ using API token
- Fetches options chain data for multiple assets (NIFTY, BANKNIFTY, FINNIFTY, etc.)
- Stores data in PostgreSQL database for time-series analysis
- Configurable asset list that can be edited by admin
- Dedicated admin API for configuration management
- Concurrent data collection for multiple assets
- Stores raw JSON responses and processed CSV files locally
- Runs periodically to keep data up-to-date
- Built-in error handling and recovery
- Audit logging for administrative actions

## Setup

1. Clone this repository
2. Set up Python environment (Python 3.10+)
3. Copy `.env.example` to `.env` and add your credentials:
   ```
   # Dhan<PERSON><PERSON> credentials
   DHAN_ACCESS_TOKEN=your_access_token_here
   DHAN_CLIENT_ID=your_client_id_here
   
   # Supabase credentials
   SUPABASE_URL=your_supabase_url_here
   SUPABASE_KEY=your_supabase_key_here
   
   # Optional: Database URL for SQL scripts
   DATABASE_URL=postgresql://user:password@host:port/database
   ```
4. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```
5. Set up the database schema:
   ```bash
   python setup_db_schema.py
   ```

## Usage

Run the options manager to start collecting data:

```bash
python options_manager.py
```

This will:
1. Initialize the database with assets and settings from configuration
2. Authenticate with DhanHQ
3. Connect to your Supabase PostgreSQL database
4. Fetch options chain data for all active assets
5. Store the data in asset-specific tables
6. Run periodically according to configured refresh interval

## Administration

### Managing Assets

Use the AdminAPI to manage assets:

```python
from admin_api import AdminAPI

# Get all assets
assets = AdminAPI.get_assets()

# Add a new asset
new_asset = {
    'asset_code': 'SENSEX',
    'security_id': 17,
    'exchange_segment': 'IDX_I',
    'lot_size': 10,
    'name': 'BSE SENSEX',
    'description': 'Bombay Stock Exchange Sensex Index',
    'is_active': True,
    'priority': 4
}
AdminAPI.create_asset(new_asset)

# Update an asset
AdminAPI.update_asset('NIFTY', {'is_active': False})

# Get settings
settings = AdminAPI.get_settings('option_chain_settings')
```

### Configuration Files

The system uses two main configuration files:

1. `option_chain_config.py` - Contains asset list and settings
2. `create_admin_tables.sql` - Database schema for admin controls
3. `create_options_tables.sql` - Schema for option chain data tables

## Data Structure

The options data is stored in asset-specific tables with the following naming convention:

- `options_nifty` - NIFTY options data
- `options_banknifty` - BANKNIFTY options data
- `raw_nifty` - Raw JSON response data for NIFTY
- `raw_banknifty` - Raw JSON response data for BANKNIFTY

Each table has the following schema:

- `id`: Primary key
- `timestamp`: When the data was collected
- `expiry_date`: Option expiry date
- `strike_price`: Strike price of the option
- `option_type`: CE (Call) or PE (Put)
- `spot_price`: Current spot price
- `asset_code`: Asset identifier (e.g., 'NIFTY', 'BANKNIFTY')
- `delta`, `gamma`, `theta`, `vega`: Option Greeks
- `last_price`: Last traded price
- `volume`: Trading volume
- `oi`: Open interest
- `iv`: Implied volatility
- `top_bid_price`, `top_bid_quantity`, `top_ask_price`, `top_ask_quantity`: Order book data

## Administrative Tables

The system includes several admin tables:

- `option_assets`: Stores asset configuration
- `app_settings`: Stores application settings as JSONB
- `scheduled_tasks`: Tracks task execution status
- `admin_audit_log`: Logs all administrative actions

## Database Queries

For time-series analysis, you can use queries like:

```sql
-- Get time series data for a specific option
SELECT timestamp, last_price, iv, delta 
FROM options_nifty 
WHERE strike_price = 23500 AND option_type = 'CE' AND expiry_date = '2025-04-03'
ORDER BY timestamp;

-- Get latest option chain snapshot for BANKNIFTY
SELECT * FROM options_banknifty 
WHERE timestamp = (SELECT MAX(timestamp) FROM options_banknifty)
ORDER BY strike_price, option_type;

-- View audit logs
SELECT * FROM admin_audit_log ORDER BY created_at DESC LIMIT 100;
```

## Views

Asset-specific views are automatically created for easier data access:

- `nifty_view` - Paired call/put data for NIFTY visualization
- `banknifty_view` - Paired call/put data for BANKNIFTY visualization

## License

[MIT License](LICENSE)
