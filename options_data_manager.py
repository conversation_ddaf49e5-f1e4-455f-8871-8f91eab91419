import os
from datetime import datetime
from dhanhq import dhanhq
from dotenv import load_dotenv
from supabase import create_client, Client
import pandas as pd
import json

# Load environment variables
load_dotenv()

# Initialize DhanHQ client
dhan = dhanhq(
    client_id=os.getenv('DHAN_CLIENT_ID'),
    access_token=os.getenv('DHAN_ACCESS_TOKEN')
)

# Initialize Supabase client
supabase: Client = create_client(
    os.getenv('SUPABASE_URL'),
    os.getenv('SUPABASE_KEY')
)

def get_nifty_expiry_dates():
    """Fetch all available Nifty expiry dates"""
    try:
        # Nifty security ID is 13 and segment is IDX_I as per documentation
        response = dhan.expiry_list(
            under_security_id=13,
            under_exchange_segment="IDX_I"
        )
        return response['data']
    except Exception as e:
        print(f"Error fetching expiry dates: {e}")
        return []

def fetch_and_store_option_chain(expiry_date):
    """Fetch option chain data for a specific expiry and store in Supabase"""
    try:
        # Fetch option chain data
        option_chain = dhan.option_chain(
            under_security_id=13,  # Nifty
            under_exchange_segment="IDX_I",
            expiry=expiry_date
        )
        
        if 'data' not in option_chain:
            print(f"No data found for expiry {expiry_date}")
            return
        
        # Extract and transform data
        spot_price = option_chain['data']['last_price']
        timestamp = datetime.now().isoformat()
        
        # Process option chain data
        records = []
        for strike, data in option_chain['data']['oc'].items():
            if 'ce' in data:
                ce_data = data['ce']
                records.append({
                    'timestamp': timestamp,
                    'expiry_date': expiry_date,
                    'strike_price': float(strike),
                    'option_type': 'CE',
                    'spot_price': spot_price,
                    'last_price': ce_data.get('last_price'),
                    'oi': ce_data.get('oi'),
                    'volume': ce_data.get('volume'),
                    'iv': ce_data.get('implied_volatility'),
                    'delta': ce_data.get('greeks', {}).get('delta'),
                    'theta': ce_data.get('greeks', {}).get('theta'),
                    'gamma': ce_data.get('greeks', {}).get('gamma'),
                    'vega': ce_data.get('greeks', {}).get('vega')
                })
            
            if 'pe' in data:
                pe_data = data['pe']
                records.append({
                    'timestamp': timestamp,
                    'expiry_date': expiry_date,
                    'strike_price': float(strike),
                    'option_type': 'PE',
                    'spot_price': spot_price,
                    'last_price': pe_data.get('last_price'),
                    'oi': pe_data.get('oi'),
                    'volume': pe_data.get('volume'),
                    'iv': pe_data.get('implied_volatility'),
                    'delta': pe_data.get('greeks', {}).get('delta'),
                    'theta': pe_data.get('greeks', {}).get('theta'),
                    'gamma': pe_data.get('greeks', {}).get('gamma'),
                    'vega': pe_data.get('greeks', {}).get('vega')
                })
        
        # Upload to Supabase
        response = supabase.table('nifty_options_data').insert(records).execute()
        print(f"Successfully uploaded {len(records)} records for expiry {expiry_date}")
        
    except Exception as e:
        print(f"Error processing expiry {expiry_date}: {e}")

def main():
    # Get all expiry dates
    expiry_dates = get_nifty_expiry_dates()
    
    if not expiry_dates:
        print("No expiry dates found")
        return
    
    print(f"Found {len(expiry_dates)} expiry dates")
    
    # Process each expiry date
    for expiry in expiry_dates:
        print(f"Processing expiry date: {expiry}")
        fetch_and_store_option_chain(expiry)

if __name__ == "__main__":
    main() 