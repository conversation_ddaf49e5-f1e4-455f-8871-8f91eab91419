-- Drop existing views if they exist
DROP VIEW IF EXISTS option_chain_detailed_view;
DROP VIEW IF EXISTS option_chain_changes_view;
DROP VIEW IF EXISTS option_chain_summary_view;
DROP VIEW IF EXISTS intraday_changes_view;

-- Create detailed option chain view that pairs CE and PE data side by side
CREATE VIEW option_chain_detailed_view AS
WITH latest_timestamps AS (
    SELECT expiry_date, MAX(timestamp) as latest_timestamp
    FROM nifty
    GROUP BY expiry_date
),
latest_data AS (
    SELECT n.*
    FROM nifty n
    INNER JOIN latest_timestamps lt 
    ON n.expiry_date = lt.expiry_date 
    AND n.timestamp = lt.latest_timestamp
),
ce_data AS (
    SELECT *
    FROM latest_data
    WHERE option_type = 'CE'
),
pe_data AS (
    SELECT *
    FROM latest_data
    WHERE option_type = 'PE'
)
SELECT 
    ce.expiry_date,
    ce.timestamp,
    ce.strike_price,
    ce.spot_price,
    ce.last_price as ce_last_price,
    pe.last_price as pe_last_price,
    ce.oi as ce_oi,
    pe.oi as pe_oi,
    ce.volume as ce_volume,
    pe.volume as pe_volume,
    ce.iv as ce_iv,
    pe.iv as pe_iv,
    ce.delta as ce_delta,
    pe.delta as pe_delta,
    ce.theta as ce_theta,
    pe.theta as pe_theta,
    (ce.oi + pe.oi) as total_oi,
    CASE 
        WHEN ce.oi > 0 THEN ROUND(CAST(pe.oi AS FLOAT) / ce.oi, 2)
        ELSE 0 
    END as pcr
FROM ce_data ce
FULL OUTER JOIN pe_data pe 
ON ce.strike_price = pe.strike_price 
AND ce.expiry_date = pe.expiry_date
WHERE ce.strike_price IS NOT NULL
ORDER BY ce.strike_price;

-- Create view to track changes in option prices and OI
CREATE VIEW option_chain_changes_view AS
WITH current_data AS (
    SELECT n.*,
           LAG(last_price) OVER (PARTITION BY expiry_date, strike_price, option_type ORDER BY timestamp) as prev_price,
           LAG(oi) OVER (PARTITION BY expiry_date, strike_price, option_type ORDER BY timestamp) as prev_oi,
           LAG(timestamp) OVER (PARTITION BY expiry_date, strike_price, option_type ORDER BY timestamp) as prev_timestamp
    FROM nifty n
)
SELECT 
    expiry_date,
    timestamp,
    strike_price,
    option_type,
    last_price,
    oi,
    (last_price - prev_price) as price_change,
    CASE 
        WHEN prev_price > 0 THEN ((last_price - prev_price) / prev_price * 100)
        ELSE 0 
    END as price_change_percent,
    (oi - prev_oi) as oi_change,
    EXTRACT(EPOCH FROM (timestamp - prev_timestamp))/60 as minutes_since_last_update
FROM current_data
WHERE prev_price IS NOT NULL;

-- Create summary view for each expiry
CREATE VIEW option_chain_summary_view AS
WITH latest_data AS (
    SELECT n.*
    FROM nifty n
    INNER JOIN (
        SELECT expiry_date, MAX(timestamp) as latest_timestamp
        FROM nifty
        GROUP BY expiry_date
    ) lt ON n.expiry_date = lt.expiry_date AND n.timestamp = lt.latest_timestamp
)
SELECT 
    expiry_date,
    timestamp,
    spot_price,
    COUNT(DISTINCT strike_price) as total_strikes,
    SUM(CASE WHEN option_type = 'CE' THEN oi ELSE 0 END) as total_ce_oi,
    SUM(CASE WHEN option_type = 'PE' THEN oi ELSE 0 END) as total_pe_oi,
    SUM(CASE WHEN option_type = 'CE' THEN volume ELSE 0 END) as total_ce_volume,
    SUM(CASE WHEN option_type = 'PE' THEN volume ELSE 0 END) as total_pe_volume,
    ROUND(AVG(iv), 2) as avg_iv,
    ROUND(AVG(CASE WHEN option_type = 'CE' THEN iv END), 2) as avg_ce_iv,
    ROUND(AVG(CASE WHEN option_type = 'PE' THEN iv END), 2) as avg_pe_iv,
    ROUND(CAST(SUM(CASE WHEN option_type = 'PE' THEN oi ELSE 0 END) AS FLOAT) / 
          NULLIF(SUM(CASE WHEN option_type = 'CE' THEN oi ELSE 0 END), 0), 2) as pcr
FROM latest_data
GROUP BY expiry_date, timestamp, spot_price;

-- Create intraday changes view
CREATE VIEW intraday_changes_view AS
WITH day_data AS (
    SELECT 
        expiry_date,
        strike_price,
        option_type,
        FIRST_VALUE(last_price) OVER (PARTITION BY expiry_date, strike_price, option_type, DATE(timestamp) ORDER BY timestamp) as day_open,
        FIRST_VALUE(oi) OVER (PARTITION BY expiry_date, strike_price, option_type, DATE(timestamp) ORDER BY timestamp) as day_open_oi,
        last_price as current_price,
        oi as current_oi,
        MAX(last_price) OVER (PARTITION BY expiry_date, strike_price, option_type, DATE(timestamp)) as day_high,
        MIN(last_price) OVER (PARTITION BY expiry_date, strike_price, option_type, DATE(timestamp)) as day_low,
        timestamp
    FROM nifty
    WHERE DATE(timestamp) = CURRENT_DATE
)
SELECT 
    expiry_date,
    strike_price,
    option_type,
    day_open,
    current_price,
    day_high,
    day_low,
    (current_price - day_open) as price_change,
    CASE 
        WHEN day_open > 0 THEN ((current_price - day_open) / day_open * 100)
        ELSE 0 
    END as price_change_percent,
    current_oi - day_open_oi as oi_change,
    timestamp
FROM day_data;

-- Example queries for Flutter:

/*
-- 1. Get detailed option chain for specific expiry
SELECT *
FROM option_chain_detailed_view
WHERE expiry_date = '2025-04-09'
ORDER BY strike_diff ASC
LIMIT 20;

-- 2. Get intraday changes for ATM options
SELECT *
FROM intraday_changes_view
WHERE expiry_date = '2025-04-09'
  AND ABS(strike_price - spot_price) < 100
ORDER BY strike_price;

-- 3. Get option chain summary
SELECT *
FROM option_chain_summary_view
ORDER BY expiry_date;

-- 4. Get significant OI changes
SELECT *
FROM option_chain_changes_view
WHERE ABS(oi_change) > 100000
  AND minutes_since_last_update < 60
ORDER BY timestamp DESC;
*/ 