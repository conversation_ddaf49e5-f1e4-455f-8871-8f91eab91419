# Database Migrations Documentation

## Overview
This document outlines the database structure, migrations, and key functions implemented for the options data management system. The database is designed to efficiently store and analyze options market data with support for time-series analysis.

## Table Structure

### 1. Primary Data Table: `nifty_timeseries`
```sql
CREATE TABLE nifty_timeseries (
    timestamp TIMESTAMPTZ NOT NULL,
    expiry_date DATE NOT NULL,
    strike_price NUMERIC(10,2) NOT NULL,
    spot_price NUMERIC(10,2) NOT NULL,
    option_type VARCHAR(2) NOT NULL,
    last_price NUMERIC(10,2) NOT NULL,
    oi INTEGER NOT NULL,
    volume INTEGER NOT NULL,
    iv NUMERIC(10,4) NOT NULL,
    delta NUMERIC(10,4) NOT NULL,
    theta NUMERIC(10,4) NOT NULL,
    gamma NUMERIC(10,4),
    vega NUMERIC(10,4),
    PRIMARY KEY (timestamp, expiry_date, strike_price, option_type)
);
```

Key Features:
- Composite primary key for unique identification
- High precision numeric fields for accurate pricing
- Timestamp with timezone for accurate time tracking
- Greeks data (delta, theta, gamma, vega) for options analysis

### 2. Expiry Dates Management: `expiry_dates`
```sql
CREATE TABLE expiry_dates (
    expiry_date DATE PRIMARY KEY,
    is_weekly BOOLEAN NOT NULL DEFAULT false,
    is_monthly BOOLEAN NOT NULL DEFAULT false,
    status VARCHAR(20) NOT NULL DEFAULT 'active',
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    last_updated TIMESTAMPTZ NOT NULL DEFAULT NOW()
);
```

Key Features:
- Automatic tracking of weekly and monthly expiries
- Status tracking for active/expired dates
- Timestamp tracking for creation and updates

## Indexes

### Primary Table Indexes
```sql
CREATE INDEX idx_nifty_timeseries_expiry_timestamp 
    ON nifty_timeseries (expiry_date, timestamp DESC);

CREATE INDEX idx_nifty_timeseries_strike_timestamp 
    ON nifty_timeseries (strike_price, timestamp DESC);

CREATE INDEX idx_nifty_timeseries_option_type_timestamp 
    ON nifty_timeseries (option_type, timestamp DESC);
```

### Expiry Dates Index
```sql
CREATE INDEX idx_expiry_dates_status 
    ON expiry_dates(status);
```

## Materialized Views

### 1. 5-Minute OHLCV Data
```sql
CREATE MATERIALIZED VIEW nifty_5min_ohlcv AS
WITH time_buckets AS (
    SELECT 
        date_trunc('hour', timestamp) + 
        INTERVAL '5 min' * (EXTRACT(MINUTE FROM timestamp)::integer / 5) AS bucket,
        expiry_date,
        strike_price,
        option_type,
        last_price,
        volume,
        oi,
        spot_price,
        iv,
        delta,
        theta
    FROM nifty_timeseries
)
SELECT 
    bucket,
    expiry_date,
    strike_price,
    option_type,
    MAX(CASE WHEN rn_first = 1 THEN last_price END) as open,
    MAX(last_price) as high,
    MIN(last_price) as low,
    MAX(CASE WHEN rn_last = 1 THEN last_price END) as close,
    SUM(volume) as volume,
    MAX(CASE WHEN rn_last = 1 THEN oi END) as oi,
    MAX(CASE WHEN rn_last = 1 THEN spot_price END) as spot_price,
    AVG(iv) as avg_iv,
    AVG(delta) as avg_delta,
    AVG(theta) as avg_theta
FROM time_buckets
GROUP BY bucket, expiry_date, strike_price, option_type;
```

### 2. Daily Statistics
```sql
CREATE MATERIALIZED VIEW nifty_daily_stats AS
WITH daily_buckets AS (
    SELECT 
        date_trunc('day', timestamp) AS day,
        expiry_date,
        strike_price,
        option_type,
        last_price,
        volume,
        oi,
        spot_price,
        iv,
        delta
    FROM nifty_timeseries
)
SELECT 
    day,
    expiry_date,
    strike_price,
    option_type,
    MAX(CASE WHEN rn_first = 1 THEN last_price END) as open_price,
    MAX(last_price) as high_price,
    MIN(last_price) as low_price,
    MAX(CASE WHEN rn_last = 1 THEN last_price END) as close_price,
    MAX(CASE WHEN rn_last = 1 THEN spot_price END) as spot_price,
    AVG(iv) as avg_iv,
    AVG(delta) as avg_delta,
    MAX(oi) as max_oi,
    MIN(oi) as min_oi,
    SUM(volume) as total_volume,
    COUNT(*) as update_count
FROM daily_buckets
GROUP BY day, expiry_date, strike_price, option_type;
```

## Key Functions

### 1. Data Validation
```sql
CREATE OR REPLACE FUNCTION validate_option_data()
RETURNS TRIGGER AS $$
BEGIN
    -- Validate option type
    IF NEW.option_type NOT IN ('CE', 'PE') THEN
        RAISE EXCEPTION 'Invalid option_type: %. Must be CE or PE', NEW.option_type;
    END IF;

    -- Validate numeric ranges
    IF NEW.strike_price <= 0 THEN
        RAISE EXCEPTION 'Invalid strike_price: %. Must be positive', NEW.strike_price;
    END IF;

    -- Additional validations...
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;
```

### 2. Expiry Date Management
```sql
CREATE OR REPLACE FUNCTION sync_expiry_dates()
RETURNS void AS $$
BEGIN
    -- Insert new expiry dates
    INSERT INTO expiry_dates (expiry_date)
    SELECT DISTINCT nt.expiry_date
    FROM nifty_timeseries nt
    WHERE NOT EXISTS (
        SELECT 1 FROM expiry_dates ed 
        WHERE ed.expiry_date = nt.expiry_date
    );

    -- Update monthly/weekly flags
    UPDATE expiry_dates
    SET is_monthly = true,
        is_weekly = false
    WHERE expiry_date IN (
        SELECT ed.expiry_date
        FROM expiry_dates ed
        WHERE EXTRACT(DOW FROM ed.expiry_date) = 4  -- Thursday
        AND ed.expiry_date = (
            SELECT MAX(inner_ed.expiry_date)
            FROM expiry_dates inner_ed
            WHERE EXTRACT(MONTH FROM inner_ed.expiry_date) = EXTRACT(MONTH FROM ed.expiry_date)
            AND EXTRACT(YEAR FROM inner_ed.expiry_date) = EXTRACT(YEAR FROM ed.expiry_date)
            AND EXTRACT(DOW FROM inner_ed.expiry_date) = 4
        )
    );
END;
$$ LANGUAGE plpgsql;
```

### 3. Price Movement Analysis
```sql
CREATE OR REPLACE FUNCTION get_price_movement(
    p_expiry_date DATE,
    p_strike_price NUMERIC,
    p_option_type VARCHAR,
    p_interval INTERVAL DEFAULT INTERVAL '5 minutes',
    p_start_time TIMESTAMPTZ DEFAULT NULL,
    p_end_time TIMESTAMPTZ DEFAULT NULL
)
RETURNS TABLE (
    bucket TIMESTAMPTZ,
    open NUMERIC,
    high NUMERIC,
    low NUMERIC,
    close NUMERIC,
    volume BIGINT,
    oi INTEGER,
    spot_price NUMERIC,
    avg_iv NUMERIC
) AS $$
-- Function implementation...
$$ LANGUAGE plpgsql;
```

### 4. IV Term Structure Analysis
```sql
CREATE OR REPLACE FUNCTION get_iv_term_structure(
    p_timestamp TIMESTAMPTZ DEFAULT NOW(),
    p_strike_offset INTEGER DEFAULT 5
)
RETURNS TABLE (
    expiry_date DATE,
    strike_price NUMERIC,
    call_iv NUMERIC,
    put_iv NUMERIC,
    atm_distance INTEGER
) AS $$
-- Function implementation...
$$ LANGUAGE SQL;
```

## Data Flow

1. **Data Ingestion**:
   - Raw options data is inserted into `nifty_timeseries`
   - Trigger `validate_option_data` ensures data quality
   - `sync_expiry_dates` keeps expiry dates updated

2. **Data Aggregation**:
   - 5-minute OHLCV data is maintained in `nifty_5min_ohlcv`
   - Daily statistics are available in `nifty_daily_stats`

3. **Analysis Functions**:
   - `get_price_movement`: Retrieves price action data
   - `get_iv_term_structure`: Analyzes volatility surface
   - Additional functions for specific analysis needs

## Best Practices

1. **Data Integrity**:
   - Use triggers for data validation
   - Maintain referential integrity
   - Regular data quality checks

2. **Performance**:
   - Appropriate indexes for common queries
   - Materialized views for aggregated data
   - Partitioning for large datasets

3. **Maintenance**:
   - Regular view refreshes
   - Archival of old data
   - Monitoring of table sizes and performance

## Common Queries

### 1. Get Latest Option Chain
```sql
SELECT *
FROM nifty_timeseries
WHERE expiry_date = :expiry_date
AND timestamp = (
    SELECT MAX(timestamp)
    FROM nifty_timeseries
    WHERE expiry_date = :expiry_date
);
```

### 2. Get Active Expiry Dates
```sql
SELECT *
FROM expiry_dates
WHERE status = 'active'
AND expiry_date >= CURRENT_DATE
ORDER BY expiry_date;
```

### 3. Get ATM Options
```sql
WITH latest_spot AS (
    SELECT DISTINCT ON (timestamp)
        timestamp,
        spot_price
    FROM nifty_timeseries
    WHERE expiry_date = :expiry_date
    ORDER BY timestamp DESC
    LIMIT 1
)
SELECT *
FROM nifty_timeseries nt
WHERE nt.timestamp = (SELECT timestamp FROM latest_spot)
AND ABS(nt.strike_price - (SELECT spot_price FROM latest_spot)) <= 100
ORDER BY ABS(nt.strike_price - (SELECT spot_price FROM latest_spot));
``` 