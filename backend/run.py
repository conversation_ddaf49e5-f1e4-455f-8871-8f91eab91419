#!/usr/bin/env python
"""
Startup script for the Trading API
"""
import os
import sys

# Add parent directory to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backend.app import app
from backend.config import API_CONFIG

if __name__ == '__main__':
    print(f"Starting Trading API on {API_CONFIG['host']}:{API_CONFIG['port']}")
    print(f"Debug mode: {API_CONFIG['debug']}")
    
    app.run(
        host=API_CONFIG['host'],
        port=API_CONFIG['port'],
        debug=API_CONFIG['debug']
    ) 