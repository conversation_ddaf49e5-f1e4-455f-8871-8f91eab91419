# Trading API Backend

This is a secure Flask-based backend API for placing orders on Dhan based on signals from MT5 indicator or other trading sources.

## Features

- **Secure Webhook Endpoint**: Receives order signals from MT5, TradingView, or custom sources
- **Account Balance Check**: Validates available balance before placing orders
- **Dynamic Position Sizing**: Calculates order quantity based on account balance and risk management rules
- **Telegram Notifications**: Sends real-time notifications for all orders and errors
- **Comprehensive Logging**: Logs all orders and errors to database
- **Security Features**:
  - API key authentication
  - IP whitelisting
  - Rate limiting
  - Request sanitization
  - CORS support

## API Endpoints

### 1. Place Order (Webhook)
**POST** `/api/v1/webhook/order`

Receives order signals from external sources.

**Headers:**
- `X-API-Key`: Your API key (required if authentication is enabled)
- `Content-Type`: application/json

**Request Body:**
```json
{
    "source": "MT5",
    "api_key": "your-api-key",
    "order": {
        "symbol": "NIFTY",
        "order_type": "BUY",
        "quantity": 2,
        "price": 20000,
        "order_category": "MARKET",
        "product_type": "INTRADAY",
        "strike_price": 20000,
        "expiry_date": "2024-01-25",
        "option_type": "CE",
        "stop_loss": 19900,
        "take_profit": 20200,
        "mt5_order_id": "MT5_123456",
        "signal_strength": 85.5
    },
    "metadata": {}
}
```

**Response:**
```json
{
    "success": true,
    "order_id": "DHAN_ORDER_123",
    "message": "Order placed successfully",
    "timestamp": "2024-01-20T10:30:00+05:30",
    "details": {}
}
```

### 2. Get Account Balance
**GET** `/api/v1/balance`

Get current account balance information.

**Response:**
```json
{
    "success": true,
    "data": {
        "available_balance": 100000.00,
        "total_balance": 120000.00,
        "used_margin": 20000.00,
        "free_margin": 100000.00,
        "timestamp": "2024-01-20T10:30:00+05:30"
    }
}
```

### 3. Get Recent Orders
**GET** `/api/v1/orders/recent?limit=10`

Get recent order history.

### 4. Get Daily Statistics
**GET** `/api/v1/stats/daily?date=2024-01-20`

Get trading statistics for a specific date.

## Configuration

Create a `.env` file in the project root with the following variables:

```env
# API Configuration
API_HOST=0.0.0.0
API_PORT=5000
API_DEBUG=False
API_SECRET_KEY=your-secret-key
API_KEY=your-api-key-for-mt5

# Dhan Configuration
DHAN_ACCESS_TOKEN=your-dhan-access-token
DHAN_CLIENT_ID=your-dhan-client-id
DHAN_TEST_MODE=False

# Telegram Configuration
TELEGRAM_BOT_TOKEN=your-telegram-bot-token
TELEGRAM_CHAT_ID=your-telegram-chat-id
TELEGRAM_ENABLED=True

# Trading Configuration
MAX_POSITION_SIZE_PERCENT=10
MIN_BALANCE_REQUIRED=10000
ALLOWED_SYMBOLS=NIFTY,BANKNIFTY,FINNIFTY
MAX_ORDERS_PER_MINUTE=10
ENABLE_MARGIN_TRADING=False

# Risk Management
MAX_DAILY_LOSS_PERCENT=5
MAX_DAILY_TRADES=50
DEFAULT_STOP_LOSS_PERCENT=2
DEFAULT_TAKE_PROFIT_PERCENT=4

# Database Configuration
SUPABASE_URL=your-supabase-url
SUPABASE_KEY=your-supabase-key

# Security Configuration
REQUIRE_API_KEY=True
ALLOWED_IPS=127.0.0.1,***********/24
RATE_LIMIT_PER_IP=100
ENABLE_CORS=False
CORS_ORIGINS=*

# Logging
LOG_LEVEL=INFO
```

## Installation

1. Install dependencies:
```bash
pip install -r requirements.txt
```

2. Create database tables:
```bash
# Run the SQL script in your Supabase SQL editor
# File: backend/create_trading_tables.sql
```

3. Run the application:
```bash
python backend/app.py
```

## Production Deployment

For production, use Gunicorn:

```bash
gunicorn -w 4 -b 0.0.0.0:5000 backend.app:app
```

## Security Considerations

1. **API Key**: Always use strong API keys and rotate them regularly
2. **HTTPS**: Deploy behind a reverse proxy with SSL/TLS
3. **IP Whitelist**: Configure allowed IPs for your MT5 server
4. **Rate Limiting**: Adjust rate limits based on your needs
5. **Monitoring**: Monitor logs and set up alerts for errors

## Error Handling

The API implements comprehensive error handling:
- Invalid requests return appropriate HTTP status codes
- All errors are logged to database
- Telegram notifications for critical errors
- Duplicate order prevention

## Testing

Test the webhook endpoint:
```bash
curl -X POST http://localhost:5000/api/v1/webhook/order \
  -H "X-API-Key: your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "source": "MT5",
    "api_key": "your-api-key",
    "order": {
        "symbol": "NIFTY",
        "order_type": "BUY",
        "order_category": "MARKET",
        "product_type": "INTRADAY"
    }
}'
```

## Monitoring

- Check logs in `backend/logs/trading_api.log`
- Monitor database tables `trading_orders` and `trading_errors`
- Set up Telegram alerts for real-time notifications 