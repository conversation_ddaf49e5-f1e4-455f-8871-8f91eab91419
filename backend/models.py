"""
Pydantic models for API request/response validation
"""
from typing import Optional, Literal
from datetime import datetime
from pydantic import BaseModel, Field, validator


class OrderRequest(BaseModel):
    """Order request from MT5 indicator"""
    symbol: str = Field(..., description="Trading symbol (e.g., NIFTY, BANKNIFTY)")
    order_type: Literal["BUY", "SELL"] = Field(..., description="Order type")
    quantity: Optional[int] = Field(None, description="Number of lots (if not provided, will be calculated based on account balance)")
    price: Optional[float] = Field(None, description="Price for limit orders")
    order_category: Literal["MARKET", "LIMIT"] = Field("MARKET", description="Order category")
    product_type: Literal["CNC", "INTRADAY", "MARGIN"] = Field("INTRADAY", description="Product type")
    strike_price: Optional[float] = Field(None, description="Strike price for options")
    expiry_date: Optional[str] = Field(None, description="Expiry date for options (YYYY-MM-DD)")
    option_type: Optional[Literal["CE", "PE"]] = Field(None, description="Option type (Call/Put)")
    stop_loss: Optional[float] = Field(None, description="Stop loss price")
    take_profit: Optional[float] = Field(None, description="Take profit price")
    mt5_order_id: Optional[str] = Field(None, description="MT5 order ID for reference")
    signal_strength: Optional[float] = Field(None, description="Signal strength (0-100)")
    
    @validator('symbol')
    def validate_symbol(cls, v):
        return v.upper()
    
    @validator('quantity')
    def validate_quantity(cls, v):
        if v is not None and v <= 0:
            raise ValueError("Quantity must be positive")
        return v
    
    @validator('signal_strength')
    def validate_signal_strength(cls, v):
        if v is not None and not (0 <= v <= 100):
            raise ValueError("Signal strength must be between 0 and 100")
        return v


class OrderResponse(BaseModel):
    """Response after placing an order"""
    success: bool
    order_id: Optional[str] = None
    message: str
    timestamp: datetime
    details: Optional[dict] = None


class AccountBalance(BaseModel):
    """Account balance information"""
    available_balance: float
    total_balance: float
    used_margin: float
    free_margin: float
    timestamp: datetime


class OrderStatus(BaseModel):
    """Order status information"""
    order_id: str
    status: Literal["PENDING", "EXECUTED", "CANCELLED", "REJECTED", "PARTIAL"]
    filled_quantity: int
    remaining_quantity: int
    average_price: float
    timestamp: datetime


class TradingStats(BaseModel):
    """Daily trading statistics"""
    date: str
    total_trades: int
    successful_trades: int
    failed_trades: int
    total_pnl: float
    win_rate: float
    max_drawdown: float
    
    
class WebhookPayload(BaseModel):
    """Webhook payload from MT5 or other sources"""
    source: Literal["MT5", "TRADINGVIEW", "CUSTOM"] = Field(..., description="Source of the signal")
    api_key: str = Field(..., description="API key for authentication")
    order: OrderRequest = Field(..., description="Order details")
    metadata: Optional[dict] = Field(None, description="Additional metadata") 