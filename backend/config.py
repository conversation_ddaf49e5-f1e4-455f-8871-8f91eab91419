"""
Backend configuration for the trading API
"""
import os
from dotenv import load_dotenv

load_dotenv()

# API Configuration
API_CONFIG = {
    'host': os.getenv('API_HOST', '0.0.0.0'),
    'port': int(os.getenv('API_PORT', 5000)),
    'debug': os.getenv('API_DEBUG', 'False').lower() == 'true',
    'secret_key': os.getenv('API_SECRET_KEY', os.urandom(24).hex()),
    'api_key': os.getenv('API_KEY'),  # API key for MT5 indicator authentication
}

# Dhan Configuration
DHAN_CONFIG = {
    'access_token': os.getenv('DHAN_ACCESS_TOKEN'),
    'client_id': os.getenv('DHAN_CLIENT_ID'),
    'test_mode': os.getenv('DHAN_TEST_MODE', 'False').lower() == 'true',
}

# Telegram Configuration
TELEGRAM_CONFIG = {
    'bot_token': os.getenv('TELEGRAM_BOT_TOKEN'),
    'chat_id': os.getenv('TELEGRAM_CHAT_ID'),
    'enabled': os.getenv('TELEGRAM_ENABLED', 'True').lower() == 'true',
}

# Trading Configuration
TRADING_CONFIG = {
    'max_position_size_percent': float(os.getenv('MAX_POSITION_SIZE_PERCENT', '10')),  # Max % of account balance per trade
    'min_balance_required': float(os.getenv('MIN_BALANCE_REQUIRED', '10000')),  # Minimum balance required to trade
    'allowed_symbols': os.getenv('ALLOWED_SYMBOLS', 'NIFTY,BANKNIFTY').split(','),
    'max_orders_per_minute': int(os.getenv('MAX_ORDERS_PER_MINUTE', '10')),
    'enable_margin_trading': os.getenv('ENABLE_MARGIN_TRADING', 'False').lower() == 'true',
}

# Risk Management
RISK_CONFIG = {
    'max_daily_loss_percent': float(os.getenv('MAX_DAILY_LOSS_PERCENT', '5')),
    'max_daily_trades': int(os.getenv('MAX_DAILY_TRADES', '50')),
    'stop_loss_percent': float(os.getenv('DEFAULT_STOP_LOSS_PERCENT', '2')),
    'take_profit_percent': float(os.getenv('DEFAULT_TAKE_PROFIT_PERCENT', '4')),
}

# Database Configuration (using existing Supabase)
DB_CONFIG = {
    'supabase_url': os.getenv('SUPABASE_URL'),
    'supabase_key': os.getenv('SUPABASE_KEY'),
}

# Logging Configuration
LOG_CONFIG = {
    'level': os.getenv('LOG_LEVEL', 'INFO'),
    'file': 'backend/logs/trading_api.log',
    'max_size': 10 * 1024 * 1024,  # 10MB
    'backup_count': 5,
}

# Security Configuration
SECURITY_CONFIG = {
    'require_api_key': os.getenv('REQUIRE_API_KEY', 'True').lower() == 'true',
    'allowed_ips': os.getenv('ALLOWED_IPS', '').split(',') if os.getenv('ALLOWED_IPS') else [],
    'rate_limit_per_ip': int(os.getenv('RATE_LIMIT_PER_IP', '100')),  # requests per minute
    'enable_cors': os.getenv('ENABLE_CORS', 'False').lower() == 'true',
    'cors_origins': os.getenv('CORS_ORIGINS', '*').split(','),
}

# Validate required configurations
def validate_config():
    """Validate that all required configurations are set"""
    errors = []
    
    if not DHAN_CONFIG['access_token']:
        errors.append("DHAN_ACCESS_TOKEN is required")
    
    if not DHAN_CONFIG['client_id']:
        errors.append("DHAN_CLIENT_ID is required")
    
    if TELEGRAM_CONFIG['enabled']:
        if not TELEGRAM_CONFIG['bot_token']:
            errors.append("TELEGRAM_BOT_TOKEN is required when Telegram is enabled")
        if not TELEGRAM_CONFIG['chat_id']:
            errors.append("TELEGRAM_CHAT_ID is required when Telegram is enabled")
    
    if SECURITY_CONFIG['require_api_key'] and not API_CONFIG['api_key']:
        errors.append("API_KEY is required when API key authentication is enabled")
    
    if not DB_CONFIG['supabase_url']:
        errors.append("SUPABASE_URL is required")
    
    if not DB_CONFIG['supabase_key']:
        errors.append("SUPABASE_KEY is required")
    
    if errors:
        raise ValueError(f"Configuration errors: {', '.join(errors)}") 