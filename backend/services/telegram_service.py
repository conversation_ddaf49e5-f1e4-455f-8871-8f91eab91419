"""
Telegram notification service
"""
import logging
import asyncio
from typing import Dict, Any
from datetime import datetime
from telegram import <PERSON><PERSON>
from telegram.error import TelegramError

from backend.config import TELEGRAM_CONFIG
from backend.models import OrderRequest, OrderResponse

logger = logging.getLogger(__name__)


class TelegramService:
    """Service for sending notifications via Telegram"""
    
    def __init__(self):
        """Initialize Telegram bot"""
        self.enabled = TELEGRAM_CONFIG['enabled']
        if self.enabled:
            self.bot = Bot(token=TELEGRAM_CONFIG['bot_token'])
            self.chat_id = TELEGRAM_CONFIG['chat_id']
        else:
            logger.info("Telegram notifications are disabled")
            
    async def send_order_notification(self, order: OrderRequest, response: OrderResponse, quantity: int):
        """Send order notification to Telegram"""
        if not self.enabled:
            return
            
        try:
            # Format the message
            if response.success:
                emoji = "✅"
                status = "SUCCESS"
            else:
                emoji = "❌"
                status = "FAILED"
                
            message = f"""
{emoji} **ORDER {status}**

**Symbol:** {order.symbol}
**Type:** {order.order_type}
**Quantity:** {quantity} lots
**Category:** {order.order_category}
**Product:** {order.product_type}

"""
            
            if order.option_type:
                message += f"""**Strike:** {order.strike_price}
**Option Type:** {order.option_type}
**Expiry:** {order.expiry_date}

"""
            
            if order.price:
                message += f"**Price:** ₹{order.price:,.2f}\n"
                
            if order.stop_loss:
                message += f"**Stop Loss:** ₹{order.stop_loss:,.2f}\n"
                
            if order.take_profit:
                message += f"**Take Profit:** ₹{order.take_profit:,.2f}\n"
                
            if order.signal_strength:
                message += f"**Signal Strength:** {order.signal_strength}%\n"
                
            message += f"\n**Time:** {response.timestamp.strftime('%Y-%m-%d %H:%M:%S IST')}\n"
            
            if response.success:
                message += f"**Order ID:** `{response.order_id}`\n"
            else:
                message += f"**Error:** {response.message}\n"
                
            if order.mt5_order_id:
                message += f"**MT5 Order ID:** {order.mt5_order_id}\n"
                
            # Send the message
            await self.bot.send_message(
                chat_id=self.chat_id,
                text=message,
                parse_mode='Markdown'
            )
            
            logger.info(f"Telegram notification sent for order {response.order_id}")
            
        except TelegramError as e:
            logger.error(f"Telegram error: {e}")
        except Exception as e:
            logger.error(f"Error sending Telegram notification: {e}")
            
    async def send_balance_alert(self, balance: float, min_required: float):
        """Send low balance alert"""
        if not self.enabled:
            return
            
        try:
            message = f"""
⚠️ **LOW BALANCE ALERT**

**Current Balance:** ₹{balance:,.2f}
**Minimum Required:** ₹{min_required:,.2f}

Trading has been paused due to insufficient balance.
Please add funds to resume trading.

**Time:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S IST')}
"""
            
            await self.bot.send_message(
                chat_id=self.chat_id,
                text=message,
                parse_mode='Markdown'
            )
            
        except Exception as e:
            logger.error(f"Error sending balance alert: {e}")
            
    async def send_daily_summary(self, stats: Dict[str, Any]):
        """Send daily trading summary"""
        if not self.enabled:
            return
            
        try:
            win_rate = (stats['successful_trades'] / stats['total_trades'] * 100) if stats['total_trades'] > 0 else 0
            
            emoji = "📈" if stats['total_pnl'] >= 0 else "📉"
            
            message = f"""
{emoji} **DAILY TRADING SUMMARY**

**Date:** {stats['date']}
**Total Trades:** {stats['total_trades']}
**Successful:** {stats['successful_trades']}
**Failed:** {stats['failed_trades']}
**Win Rate:** {win_rate:.1f}%

**Total P&L:** ₹{stats['total_pnl']:,.2f}
**Max Drawdown:** {stats['max_drawdown']:.1f}%

Have a great day! 🚀
"""
            
            await self.bot.send_message(
                chat_id=self.chat_id,
                text=message,
                parse_mode='Markdown'
            )
            
        except Exception as e:
            logger.error(f"Error sending daily summary: {e}")
            
    async def send_error_alert(self, error_message: str, context: Dict[str, Any] = None):
        """Send error alert"""
        if not self.enabled:
            return
            
        try:
            message = f"""
🚨 **ERROR ALERT**

**Error:** {error_message}

"""
            
            if context:
                message += "**Context:**\n"
                for key, value in context.items():
                    message += f"• {key}: {value}\n"
                    
            message += f"\n**Time:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S IST')}"
            
            await self.bot.send_message(
                chat_id=self.chat_id,
                text=message,
                parse_mode='Markdown'
            )
            
        except Exception as e:
            logger.error(f"Error sending error alert: {e}")
            
    def send_order_notification_sync(self, order: OrderRequest, response: OrderResponse, quantity: int):
        """Synchronous wrapper for sending order notification"""
        if not self.enabled:
            return
            
        try:
            # Create new event loop if none exists
            try:
                loop = asyncio.get_event_loop()
            except RuntimeError:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
            # Run the async function
            loop.run_until_complete(self.send_order_notification(order, response, quantity))
            
        except Exception as e:
            logger.error(f"Error in sync wrapper: {e}")
            
    def send_error_alert_sync(self, error_message: str, context: Dict[str, Any] = None):
        """Synchronous wrapper for sending error alert"""
        if not self.enabled:
            return
            
        try:
            # Create new event loop if none exists
            try:
                loop = asyncio.get_event_loop()
            except RuntimeError:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
            # Run the async function
            loop.run_until_complete(self.send_error_alert(error_message, context))
            
        except Exception as e:
            logger.error(f"Error in sync wrapper: {e}") 