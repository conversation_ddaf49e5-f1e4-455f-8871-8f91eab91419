"""
Dhan service for order placement and account management
"""
import logging
from typing import Optional, Dict, Any
from datetime import datetime
import pytz
from dhanhq import dhanhq

from backend.config import DHAN_CONFIG, TRADING_CONFIG, RISK_CONFIG
from backend.models import OrderRequest, AccountBalance

logger = logging.getLogger(__name__)


class DhanService:
    """Service for interacting with Dhan API"""
    
    def __init__(self):
        """Initialize Dhan client"""
        self.client = dhanhq(
            client_id=DHAN_CONFIG['client_id'],
            access_token=DHAN_CONFIG['access_token']
        )
        self.ist = pytz.timezone('Asia/Kolkata')
        
    def get_account_balance(self) -> AccountBalance:
        """Get current account balance"""
        try:
            funds = self.client.get_fund_limits()
            
            if funds.get('status') == 'success' and funds.get('data'):
                data = funds['data']
                
                # Extract balance information
                available_balance = float(data.get('available_balance', 0))
                total_balance = float(data.get('net_balance', 0))
                used_margin = float(data.get('utilized_amount', 0))
                free_margin = available_balance
                
                return AccountBalance(
                    available_balance=available_balance,
                    total_balance=total_balance,
                    used_margin=used_margin,
                    free_margin=free_margin,
                    timestamp=datetime.now(self.ist)
                )
            else:
                logger.error(f"Failed to get fund limits: {funds}")
                raise Exception("Failed to get account balance")
                
        except Exception as e:
            logger.error(f"Error getting account balance: {e}")
            raise
            
    def calculate_position_size(self, order: OrderRequest, balance: AccountBalance) -> int:
        """Calculate position size based on account balance and risk management"""
        try:
            # Get the maximum position value based on percentage
            max_position_value = balance.available_balance * (TRADING_CONFIG['max_position_size_percent'] / 100)
            
            # Get lot size for the symbol
            lot_size = self.get_lot_size(order.symbol)
            
            # If quantity is provided, validate it
            if order.quantity:
                position_value = order.quantity * lot_size * (order.price or self.get_current_price(order.symbol))
                if position_value > max_position_value:
                    logger.warning(f"Requested position size exceeds maximum allowed. Adjusting...")
                    return int(max_position_value / (lot_size * (order.price or self.get_current_price(order.symbol))))
                return order.quantity
            
            # Calculate quantity based on available balance
            current_price = order.price or self.get_current_price(order.symbol)
            quantity = int(max_position_value / (lot_size * current_price))
            
            # Ensure at least 1 lot
            return max(1, quantity)
            
        except Exception as e:
            logger.error(f"Error calculating position size: {e}")
            return 1  # Default to 1 lot
            
    def get_lot_size(self, symbol: str) -> int:
        """Get lot size for a symbol"""
        # For now, using hardcoded values
        lot_sizes = {
            'NIFTY': 50,
            'BANKNIFTY': 25,
            'FINNIFTY': 40
        }
        return lot_sizes.get(symbol, 1)
        
    def get_current_price(self, symbol: str) -> float:
        """Get current price for a symbol"""
        try:
            # This would need to be implemented based on Dhan's API
            # For now, returning a placeholder
            logger.warning(f"Getting current price for {symbol} - not implemented yet")
            return 20000.0  # Placeholder
        except Exception as e:
            logger.error(f"Error getting current price: {e}")
            raise
            
    def get_security_id(self, order: OrderRequest) -> int:
        """Get security ID for the order"""
        # This would need to be implemented based on Dhan's security master
        # For now, using basic mapping
        security_ids = {
            'NIFTY': 13,
            'BANKNIFTY': 11,
            'FINNIFTY': 27
        }
        return security_ids.get(order.symbol, 0)
        
    def place_order(self, order: OrderRequest, quantity: int) -> Dict[str, Any]:
        """Place order on Dhan"""
        try:
            # Prepare order parameters
            order_params = {
                'transaction_type': self.get_transaction_type(order.order_type),
                'exchange_segment': self.get_exchange_segment(order),
                'product_type': self.get_product_type(order.product_type),
                'order_type': self.get_order_category(order.order_category),
                'validity': 'DAY',
                'security_id': self.get_security_id(order),
                'quantity': quantity * self.get_lot_size(order.symbol),
                'disclosed_quantity': 0,
                'after_market_order': False
            }
            
            # Add price for limit orders
            if order.order_category == "LIMIT":
                order_params['price'] = order.price
                
            # Add stop loss if provided
            if order.stop_loss:
                order_params['trigger_price'] = order.stop_loss
                
            logger.info(f"Placing order with params: {order_params}")
            
            # Place the order
            response = self.client.place_order(**order_params)
            
            if response.get('status') == 'success':
                order_id = response.get('data', {}).get('order_id')
                logger.info(f"Order placed successfully. Order ID: {order_id}")
                
                # Place stop loss order if specified
                if order.stop_loss and order_id:
                    self.place_stop_loss_order(order, quantity, order_id)
                    
                # Place take profit order if specified
                if order.take_profit and order_id:
                    self.place_take_profit_order(order, quantity, order_id)
                    
                return {
                    'success': True,
                    'order_id': order_id,
                    'details': response.get('data', {})
                }
            else:
                logger.error(f"Failed to place order: {response}")
                return {
                    'success': False,
                    'error': response.get('remarks', 'Unknown error'),
                    'details': response
                }
                
        except Exception as e:
            logger.error(f"Error placing order: {e}")
            return {
                'success': False,
                'error': str(e)
            }
            
    def place_stop_loss_order(self, order: OrderRequest, quantity: int, parent_order_id: str):
        """Place stop loss order"""
        try:
            sl_params = {
                'transaction_type': self.get_opposite_transaction_type(order.order_type),
                'exchange_segment': self.get_exchange_segment(order),
                'product_type': self.get_product_type(order.product_type),
                'order_type': 'STOP_LOSS',  # Using string instead of enum
                'validity': 'DAY',
                'security_id': self.get_security_id(order),
                'quantity': quantity * self.get_lot_size(order.symbol),
                'trigger_price': order.stop_loss,
                'disclosed_quantity': 0,
                'after_market_order': False
            }
            
            response = self.client.place_order(**sl_params)
            if response.get('status') == 'success':
                logger.info(f"Stop loss order placed successfully for parent order {parent_order_id}")
            else:
                logger.error(f"Failed to place stop loss order: {response}")
                
        except Exception as e:
            logger.error(f"Error placing stop loss order: {e}")
            
    def place_take_profit_order(self, order: OrderRequest, quantity: int, parent_order_id: str):
        """Place take profit order"""
        try:
            tp_params = {
                'transaction_type': self.get_opposite_transaction_type(order.order_type),
                'exchange_segment': self.get_exchange_segment(order),
                'product_type': self.get_product_type(order.product_type),
                'order_type': 'LIMIT',  # Using string instead of enum
                'validity': 'DAY',
                'security_id': self.get_security_id(order),
                'quantity': quantity * self.get_lot_size(order.symbol),
                'price': order.take_profit,
                'disclosed_quantity': 0,
                'after_market_order': False
            }
            
            response = self.client.place_order(**tp_params)
            if response.get('status') == 'success':
                logger.info(f"Take profit order placed successfully for parent order {parent_order_id}")
            else:
                logger.error(f"Failed to place take profit order: {response}")
                
        except Exception as e:
            logger.error(f"Error placing take profit order: {e}")
            
    def get_transaction_type(self, order_type: str):
        """Convert order type to Dhan transaction type"""
        return 'BUY' if order_type == "BUY" else 'SELL'
        
    def get_opposite_transaction_type(self, order_type: str):
        """Get opposite transaction type for SL/TP orders"""
        return 'SELL' if order_type == "BUY" else 'BUY'
        
    def get_exchange_segment(self, order: OrderRequest):
        """Get exchange segment based on order"""
        if order.option_type:
            return 'NSE_FNO'
        return 'NSE_EQ'
        
    def get_product_type(self, product_type: str):
        """Convert product type to Dhan product type"""
        mapping = {
            'CNC': 'CNC',
            'INTRADAY': 'INTRA',
            'MARGIN': 'MARGIN'
        }
        return mapping.get(product_type, 'INTRA')
        
    def get_order_category(self, order_category: str):
        """Convert order category to Dhan order type"""
        return 'MARKET' if order_category == "MARKET" else 'LIMIT'
        
    def check_daily_limits(self) -> Dict[str, Any]:
        """Check if daily trading limits are exceeded"""
        try:
            # Get today's orders
            orders = self.client.get_order_list()
            
            if orders.get('status') == 'success' and orders.get('data'):
                today = datetime.now(self.ist).date()
                today_orders = [
                    o for o in orders['data'] 
                    if datetime.fromisoformat(o.get('created_at', '')).date() == today
                ]
                
                # Count today's trades
                total_trades = len(today_orders)
                
                # Calculate today's P&L (this is simplified, actual implementation would be more complex)
                total_pnl = 0  # Would need to calculate from executed orders
                
                return {
                    'within_limits': total_trades < RISK_CONFIG['max_daily_trades'],
                    'total_trades': total_trades,
                    'max_trades': RISK_CONFIG['max_daily_trades'],
                    'total_pnl': total_pnl,
                    'max_loss': -RISK_CONFIG['max_daily_loss_percent']
                }
            
            return {
                'within_limits': True,
                'total_trades': 0,
                'max_trades': RISK_CONFIG['max_daily_trades']
            }
            
        except Exception as e:
            logger.error(f"Error checking daily limits: {e}")
            return {
                'within_limits': True,
                'error': str(e)
            } 