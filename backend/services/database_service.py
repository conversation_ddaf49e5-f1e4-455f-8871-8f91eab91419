"""
Database service for logging trades and orders
"""
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime, date
import pytz
from supabase import create_client, Client

from backend.config import DB_CONFIG
from backend.models import OrderRequest, OrderResponse

logger = logging.getLogger(__name__)


class DatabaseService:
    """Service for database operations"""
    
    def __init__(self):
        """Initialize Supabase client"""
        self.client: Client = create_client(
            DB_CONFIG['supabase_url'],
            DB_CONFIG['supabase_key']
        )
        self.ist = pytz.timezone('Asia/Kolkata')
        
    def log_order(self, order: OrderRequest, response: OrderResponse, quantity: int, balance: float):
        """Log order to database"""
        try:
            record = {
                'timestamp': response.timestamp.isoformat(),
                'source': 'MT5',
                'symbol': order.symbol,
                'order_type': order.order_type,
                'quantity': quantity,
                'price': order.price,
                'order_category': order.order_category,
                'product_type': order.product_type,
                'strike_price': order.strike_price,
                'expiry_date': order.expiry_date,
                'option_type': order.option_type,
                'stop_loss': order.stop_loss,
                'take_profit': order.take_profit,
                'mt5_order_id': order.mt5_order_id,
                'signal_strength': order.signal_strength,
                'order_id': response.order_id,
                'success': response.success,
                'message': response.message,
                'account_balance': balance,
                'details': response.details
            }
            
            result = self.client.table('trading_orders').insert(record).execute()
            logger.info(f"Order logged to database: {response.order_id}")
            
        except Exception as e:
            logger.error(f"Error logging order to database: {e}")
            
    def log_error(self, error_type: str, error_message: str, context: Dict[str, Any] = None):
        """Log error to database"""
        try:
            record = {
                'timestamp': datetime.now(self.ist).isoformat(),
                'error_type': error_type,
                'error_message': error_message,
                'context': context or {}
            }
            
            self.client.table('trading_errors').insert(record).execute()
            logger.info(f"Error logged to database: {error_type}")
            
        except Exception as e:
            logger.error(f"Error logging to database: {e}")
            
    def get_daily_stats(self, trading_date: Optional[date] = None) -> Dict[str, Any]:
        """Get daily trading statistics"""
        try:
            if not trading_date:
                trading_date = datetime.now(self.ist).date()
                
            start_time = datetime.combine(trading_date, datetime.min.time()).replace(tzinfo=self.ist).isoformat()
            end_time = datetime.combine(trading_date, datetime.max.time()).replace(tzinfo=self.ist).isoformat()
            
            # Get all orders for the day
            response = self.client.table('trading_orders')\
                .select('*')\
                .gte('timestamp', start_time)\
                .lte('timestamp', end_time)\
                .execute()
                
            orders = response.data if response.data else []
            
            # Calculate statistics
            total_trades = len(orders)
            successful_trades = len([o for o in orders if o['success']])
            failed_trades = total_trades - successful_trades
            
            # Calculate P&L (would need actual execution data)
            total_pnl = 0  # Placeholder
            max_drawdown = 0  # Placeholder
            
            return {
                'date': trading_date.isoformat(),
                'total_trades': total_trades,
                'successful_trades': successful_trades,
                'failed_trades': failed_trades,
                'total_pnl': total_pnl,
                'win_rate': (successful_trades / total_trades * 100) if total_trades > 0 else 0,
                'max_drawdown': max_drawdown
            }
            
        except Exception as e:
            logger.error(f"Error getting daily stats: {e}")
            return {
                'date': trading_date.isoformat() if trading_date else '',
                'total_trades': 0,
                'successful_trades': 0,
                'failed_trades': 0,
                'total_pnl': 0,
                'win_rate': 0,
                'max_drawdown': 0
            }
            
    def get_recent_orders(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent orders"""
        try:
            response = self.client.table('trading_orders')\
                .select('*')\
                .order('timestamp', desc=True)\
                .limit(limit)\
                .execute()
                
            return response.data if response.data else []
            
        except Exception as e:
            logger.error(f"Error getting recent orders: {e}")
            return []
            
    def check_duplicate_order(self, mt5_order_id: str) -> bool:
        """Check if MT5 order already exists"""
        try:
            if not mt5_order_id:
                return False
                
            response = self.client.table('trading_orders')\
                .select('id')\
                .eq('mt5_order_id', mt5_order_id)\
                .execute()
                
            return len(response.data) > 0 if response.data else False
            
        except Exception as e:
            logger.error(f"Error checking duplicate order: {e}")
            return False
            
    def create_tables_if_not_exists(self):
        """Create required tables if they don't exist"""
        try:
            # Note: In Supabase, you typically create tables through the web interface
            # or using migrations. This is just a placeholder to show the schema
            
            logger.info("Database tables should be created through Supabase interface:")
            logger.info("""
            -- Trading orders table
            CREATE TABLE IF NOT EXISTS trading_orders (
                id SERIAL PRIMARY KEY,
                timestamp TIMESTAMPTZ NOT NULL,
                source VARCHAR(50),
                symbol VARCHAR(50) NOT NULL,
                order_type VARCHAR(10) NOT NULL,
                quantity INTEGER NOT NULL,
                price DECIMAL(10,2),
                order_category VARCHAR(20),
                product_type VARCHAR(20),
                strike_price DECIMAL(10,2),
                expiry_date DATE,
                option_type VARCHAR(5),
                stop_loss DECIMAL(10,2),
                take_profit DECIMAL(10,2),
                mt5_order_id VARCHAR(100),
                signal_strength DECIMAL(5,2),
                order_id VARCHAR(100),
                success BOOLEAN NOT NULL,
                message TEXT,
                account_balance DECIMAL(15,2),
                details JSONB,
                created_at TIMESTAMPTZ DEFAULT NOW()
            );
            
            -- Trading errors table
            CREATE TABLE IF NOT EXISTS trading_errors (
                id SERIAL PRIMARY KEY,
                timestamp TIMESTAMPTZ NOT NULL,
                error_type VARCHAR(100) NOT NULL,
                error_message TEXT NOT NULL,
                context JSONB,
                created_at TIMESTAMPTZ DEFAULT NOW()
            );
            
            -- Create indexes
            CREATE INDEX idx_trading_orders_timestamp ON trading_orders(timestamp);
            CREATE INDEX idx_trading_orders_symbol ON trading_orders(symbol);
            CREATE INDEX idx_trading_orders_mt5_order_id ON trading_orders(mt5_order_id);
            CREATE INDEX idx_trading_errors_timestamp ON trading_errors(timestamp);
            """)
            
        except Exception as e:
            logger.error(f"Error in create_tables_if_not_exists: {e}") 