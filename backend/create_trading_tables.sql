-- Trading orders table for logging all order requests and responses
CREATE TABLE IF NOT EXISTS trading_orders (
    id SERIAL PRIMARY KEY,
    timestamp TIMESTAMPTZ NOT NULL,
    source VARCHAR(50),
    symbol VARCHAR(50) NOT NULL,
    order_type VARCHAR(10) NOT NULL,
    quantity INTEGER NOT NULL,
    price DECIMAL(10,2),
    order_category VARCHAR(20),
    product_type VARCHAR(20),
    strike_price DECIMAL(10,2),
    expiry_date DATE,
    option_type VARCHAR(5),
    stop_loss DECIMAL(10,2),
    take_profit DECIMAL(10,2),
    mt5_order_id VARCHAR(100),
    signal_strength DECIMAL(5,2),
    order_id VARCHAR(100),
    success BOOLEAN NOT NULL,
    message TEXT,
    account_balance DECIMAL(15,2),
    details JSONB,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Trading errors table for logging all errors
CREATE TABLE IF NOT EXISTS trading_errors (
    id SERIAL PRIMARY KEY,
    timestamp TIMESTAMPTZ NOT NULL,
    error_type VARCHAR(100) NOT NULL,
    error_message TEXT NOT NULL,
    context JSONB,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_trading_orders_timestamp ON trading_orders(timestamp);
CREATE INDEX IF NOT EXISTS idx_trading_orders_symbol ON trading_orders(symbol);
CREATE INDEX IF NOT EXISTS idx_trading_orders_mt5_order_id ON trading_orders(mt5_order_id);
CREATE INDEX IF NOT EXISTS idx_trading_orders_success ON trading_orders(success);
CREATE INDEX IF NOT EXISTS idx_trading_orders_created_at ON trading_orders(created_at);
CREATE INDEX IF NOT EXISTS idx_trading_errors_timestamp ON trading_errors(timestamp);
CREATE INDEX IF NOT EXISTS idx_trading_errors_error_type ON trading_errors(error_type);

-- Comments
COMMENT ON TABLE trading_orders IS 'Stores all trading order requests and their execution results';
COMMENT ON TABLE trading_errors IS 'Stores all trading system errors for debugging and monitoring';

-- Grant permissions (adjust as needed)
GRANT SELECT, INSERT, UPDATE ON trading_orders TO authenticated;
GRANT SELECT, INSERT ON trading_errors TO authenticated; 