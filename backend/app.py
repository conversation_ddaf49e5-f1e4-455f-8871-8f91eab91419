"""
Main Flask application for trading API
"""
import os
import logging
from datetime import datetime
from flask import Flask, request, jsonify, g
from flask_cors import CORS
from pydantic import ValidationError
import pytz

from backend.config import (
    API_CONFIG, SECURITY_CONFIG, LOG_CONFIG, TRADING_CONFIG,
    validate_config
)
from backend.models import WebhookPayload, OrderRequest, OrderResponse
from backend.services.dhan_service import DhanService
from backend.services.telegram_service import TelegramService
from backend.services.database_service import DatabaseService
from backend.utils.security import require_auth, validate_webhook_source, sanitize_order_data

# Setup logging
os.makedirs(os.path.dirname(LOG_CONFIG['file']), exist_ok=True)
logging.basicConfig(
    level=getattr(logging, LOG_CONFIG['level']),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(LOG_CONFIG['file']),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Create Flask app
app = Flask(__name__)
app.config['SECRET_KEY'] = API_CONFIG['secret_key']

# Setup CORS if enabled
if SECURITY_CONFIG['enable_cors']:
    CORS(app, origins=SECURITY_CONFIG['cors_origins'])

# Initialize services
dhan_service = DhanService()
telegram_service = TelegramService()
db_service = DatabaseService()

# Timezone
ist = pytz.timezone('Asia/Kolkata')


@app.before_first_request
def initialize():
    """Initialize the application"""
    try:
        # Validate configuration
        validate_config()
        
        # Create database tables if needed
        db_service.create_tables_if_not_exists()
        
        logger.info("Trading API initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize API: {e}")
        raise


@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now(ist).isoformat(),
        'version': '1.0.0'
    })


@app.route('/api/v1/webhook/order', methods=['POST'])
@require_auth
def webhook_order():
    """
    Webhook endpoint for receiving orders from MT5 or other sources
    
    Expected payload:
    {
        "source": "MT5",
        "api_key": "your-api-key",
        "order": {
            "symbol": "NIFTY",
            "order_type": "BUY",
            "quantity": 2,  // optional
            "price": 20000,  // optional for limit orders
            "order_category": "MARKET",
            "product_type": "INTRADAY",
            // ... other order fields
        },
        "metadata": {}  // optional
    }
    """
    try:
        # Get and validate payload
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'error': 'No data provided'
            }), 400
            
        # Parse webhook payload
        try:
            webhook = WebhookPayload(**data)
        except ValidationError as e:
            return jsonify({
                'success': False,
                'error': 'Invalid payload format',
                'details': e.errors()
            }), 400
            
        # Validate source and API key
        if not validate_webhook_source(webhook.source, webhook.api_key):
            logger.warning(f"Invalid webhook source or API key from {g.client_ip}")
            return jsonify({
                'success': False,
                'error': 'Invalid source or API key'
            }), 401
            
        # Sanitize order data
        order_dict = sanitize_order_data(webhook.order.dict())
        order = OrderRequest(**order_dict)
        
        # Check if order already exists (prevent duplicates)
        if order.mt5_order_id and db_service.check_duplicate_order(order.mt5_order_id):
            logger.warning(f"Duplicate order detected: {order.mt5_order_id}")
            return jsonify({
                'success': False,
                'error': 'Duplicate order',
                'order_id': order.mt5_order_id
            }), 409
            
        # Validate symbol
        if order.symbol not in TRADING_CONFIG['allowed_symbols']:
            return jsonify({
                'success': False,
                'error': f"Symbol {order.symbol} not allowed"
            }), 400
            
        # Check daily limits
        daily_limits = dhan_service.check_daily_limits()
        if not daily_limits.get('within_limits', True):
            error_msg = f"Daily trading limit exceeded: {daily_limits['total_trades']}/{daily_limits['max_trades']} trades"
            logger.warning(error_msg)
            
            # Send notification
            telegram_service.send_error_alert_sync(error_msg, daily_limits)
            
            return jsonify({
                'success': False,
                'error': error_msg,
                'details': daily_limits
            }), 429
            
        # Get account balance
        try:
            balance = dhan_service.get_account_balance()
        except Exception as e:
            error_msg = f"Failed to get account balance: {str(e)}"
            logger.error(error_msg)
            
            # Log error
            db_service.log_error('BALANCE_ERROR', error_msg, {
                'order': order.dict(),
                'source': webhook.source
            })
            
            # Send notification
            telegram_service.send_error_alert_sync(error_msg, {'order': order.dict()})
            
            return jsonify({
                'success': False,
                'error': 'Failed to get account balance'
            }), 500
            
        # Check minimum balance
        if balance.available_balance < TRADING_CONFIG['min_balance_required']:
            error_msg = f"Insufficient balance: ₹{balance.available_balance:,.2f}"
            logger.warning(error_msg)
            
            # Send low balance alert
            telegram_service.send_balance_alert(
                balance.available_balance,
                TRADING_CONFIG['min_balance_required']
            )
            
            return jsonify({
                'success': False,
                'error': error_msg,
                'available_balance': balance.available_balance,
                'required_balance': TRADING_CONFIG['min_balance_required']
            }), 400
            
        # Calculate position size
        quantity = dhan_service.calculate_position_size(order, balance)
        logger.info(f"Calculated position size: {quantity} lots for {order.symbol}")
        
        # Place the order
        result = dhan_service.place_order(order, quantity)
        
        # Create response
        response = OrderResponse(
            success=result['success'],
            order_id=result.get('order_id'),
            message=result.get('error', 'Order placed successfully'),
            timestamp=datetime.now(ist),
            details=result.get('details')
        )
        
        # Log order to database
        db_service.log_order(order, response, quantity, balance.available_balance)
        
        # Send Telegram notification
        telegram_service.send_order_notification_sync(order, response, quantity)
        
        # Log success or failure
        if response.success:
            logger.info(f"Order placed successfully: {response.order_id}")
        else:
            logger.error(f"Order failed: {response.message}")
            
            # Log error
            db_service.log_error('ORDER_FAILED', response.message, {
                'order': order.dict(),
                'result': result
            })
            
        return jsonify(response.dict()), 200 if response.success else 400
        
    except Exception as e:
        error_msg = f"Unexpected error processing order: {str(e)}"
        logger.error(error_msg, exc_info=True)
        
        # Log error
        db_service.log_error('UNEXPECTED_ERROR', error_msg, {
            'request_data': request.get_json()
        })
        
        # Send error notification
        telegram_service.send_error_alert_sync(error_msg)
        
        return jsonify({
            'success': False,
            'error': 'Internal server error',
            'message': str(e)
        }), 500


@app.route('/api/v1/balance', methods=['GET'])
@require_auth
def get_balance():
    """Get current account balance"""
    try:
        balance = dhan_service.get_account_balance()
        return jsonify({
            'success': True,
            'data': balance.dict()
        })
    except Exception as e:
        logger.error(f"Error getting balance: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@app.route('/api/v1/orders/recent', methods=['GET'])
@require_auth
def get_recent_orders():
    """Get recent orders"""
    try:
        limit = request.args.get('limit', 10, type=int)
        orders = db_service.get_recent_orders(limit)
        
        return jsonify({
            'success': True,
            'data': orders,
            'count': len(orders)
        })
    except Exception as e:
        logger.error(f"Error getting recent orders: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@app.route('/api/v1/stats/daily', methods=['GET'])
@require_auth
def get_daily_stats():
    """Get daily trading statistics"""
    try:
        date_str = request.args.get('date')
        trading_date = None
        
        if date_str:
            trading_date = datetime.strptime(date_str, '%Y-%m-%d').date()
            
        stats = db_service.get_daily_stats(trading_date)
        
        return jsonify({
            'success': True,
            'data': stats
        })
    except Exception as e:
        logger.error(f"Error getting daily stats: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@app.errorhandler(404)
def not_found(error):
    """Handle 404 errors"""
    return jsonify({
        'success': False,
        'error': 'Endpoint not found'
    }), 404


@app.errorhandler(500)
def internal_error(error):
    """Handle 500 errors"""
    logger.error(f"Internal server error: {error}")
    return jsonify({
        'success': False,
        'error': 'Internal server error'
    }), 500


if __name__ == '__main__':
    app.run(
        host=API_CONFIG['host'],
        port=API_CONFIG['port'],
        debug=API_CONFIG['debug']
    ) 