"""
Security utilities for API authentication and rate limiting
"""
import logging
from functools import wraps
from typing import Dict, Optional
from datetime import datetime, timedelta
from flask import request, jsonify, g
import ipaddress

from backend.config import SECURITY_CONFIG, API_CONFIG

logger = logging.getLogger(__name__)

# In-memory storage for rate limiting (in production, use Redis)
rate_limit_storage: Dict[str, Dict[str, any]] = {}


def validate_api_key(api_key: str) -> bool:
    """Validate API key"""
    if not SECURITY_CONFIG['require_api_key']:
        return True
        
    return api_key == API_CONFIG['api_key']


def check_ip_whitelist(client_ip: str) -> bool:
    """Check if client IP is in whitelist"""
    allowed_ips = SECURITY_CONFIG['allowed_ips']
    
    # If no IPs specified, allow all
    if not allowed_ips or (len(allowed_ips) == 1 and allowed_ips[0] == ''):
        return True
        
    # Check direct IP match
    if client_ip in allowed_ips:
        return True
        
    # Check CIDR ranges
    for allowed_ip in allowed_ips:
        try:
            if '/' in allowed_ip:
                network = ipaddress.ip_network(allowed_ip, strict=False)
                if ipaddress.ip_address(client_ip) in network:
                    return True
        except Exception:
            continue
            
    return False


def check_rate_limit(client_ip: str) -> bool:
    """Check if client has exceeded rate limit"""
    current_time = datetime.now()
    minute_ago = current_time - timedelta(minutes=1)
    
    # Clean up old entries
    for ip in list(rate_limit_storage.keys()):
        rate_limit_storage[ip] = {
            'requests': [
                req_time for req_time in rate_limit_storage[ip].get('requests', [])
                if req_time > minute_ago
            ]
        }
        if not rate_limit_storage[ip]['requests']:
            del rate_limit_storage[ip]
    
    # Check current IP
    if client_ip not in rate_limit_storage:
        rate_limit_storage[client_ip] = {'requests': []}
        
    requests_in_minute = len(rate_limit_storage[client_ip]['requests'])
    
    if requests_in_minute >= SECURITY_CONFIG['rate_limit_per_ip']:
        return False
        
    # Add current request
    rate_limit_storage[client_ip]['requests'].append(current_time)
    return True


def get_client_ip() -> str:
    """Get client IP address"""
    # Check for proxy headers
    if request.headers.get('X-Forwarded-For'):
        return request.headers.get('X-Forwarded-For').split(',')[0].strip()
    elif request.headers.get('X-Real-IP'):
        return request.headers.get('X-Real-IP')
    else:
        return request.remote_addr


def require_auth(f):
    """Decorator to require authentication"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # Get client IP
        client_ip = get_client_ip()
        g.client_ip = client_ip
        
        # Check IP whitelist
        if not check_ip_whitelist(client_ip):
            logger.warning(f"Blocked request from unauthorized IP: {client_ip}")
            return jsonify({
                'success': False,
                'error': 'Unauthorized IP address'
            }), 403
            
        # Check rate limit
        if not check_rate_limit(client_ip):
            logger.warning(f"Rate limit exceeded for IP: {client_ip}")
            return jsonify({
                'success': False,
                'error': 'Rate limit exceeded. Please try again later.'
            }), 429
            
        # Check API key
        if SECURITY_CONFIG['require_api_key']:
            api_key = request.headers.get('X-API-Key') or request.args.get('api_key')
            
            if not api_key:
                return jsonify({
                    'success': False,
                    'error': 'API key required'
                }), 401
                
            if not validate_api_key(api_key):
                logger.warning(f"Invalid API key attempt from IP: {client_ip}")
                return jsonify({
                    'success': False,
                    'error': 'Invalid API key'
                }), 401
                
        return f(*args, **kwargs)
        
    return decorated_function


def validate_webhook_source(source: str, api_key: str) -> bool:
    """Validate webhook source"""
    valid_sources = ['MT5', 'TRADINGVIEW', 'CUSTOM']
    
    if source not in valid_sources:
        return False
        
    # Additional source-specific validation can be added here
    return validate_api_key(api_key)


def sanitize_order_data(order_data: Dict) -> Dict:
    """Sanitize order data to prevent injection attacks"""
    # Remove any potentially dangerous fields
    dangerous_fields = ['__proto__', 'constructor', 'prototype']
    
    for field in dangerous_fields:
        order_data.pop(field, None)
        
    # Validate numeric fields
    numeric_fields = ['quantity', 'price', 'stop_loss', 'take_profit', 'strike_price', 'signal_strength']
    
    for field in numeric_fields:
        if field in order_data and order_data[field] is not None:
            try:
                if field in ['quantity']:
                    order_data[field] = int(order_data[field])
                else:
                    order_data[field] = float(order_data[field])
            except (ValueError, TypeError):
                order_data[field] = None
                
    return order_data 