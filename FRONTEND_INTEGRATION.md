# Frontend Integration Guide

This guide explains how to integrate with the options data API using Supabase client in your frontend application.

## Table of Contents
- [Setup](#setup)
- [Data Models](#data-models)
- [Common Queries](#common-queries)
- [Visualization Examples](#visualization-examples)
- [Real-time Updates](#real-time-updates)
- [Error Handling](#error-handling)

## Setup

### 1. Install Required Packages

```bash
# Using npm
npm install @supabase/supabase-js chart.js react-chartjs-2 @mui/material @mui/x-data-grid dayjs

# Using yarn
yarn add @supabase/supabase-js chart.js react-chartjs-2 @mui/material @mui/x-data-grid dayjs
```

### 2. Initialize Supabase Client

```typescript
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.REACT_APP_SUPABASE_URL
const supabaseKey = process.env.REACT_APP_SUPABASE_KEY

const supabase = createClient(supabaseUrl, supabaseKey)
```

## Data Models

### Option Chain Data Structure
```typescript
interface OptionData {
  timestamp: string;
  expiry_date: string;
  strike_price: number;
  option_type: 'CE' | 'PE';
  spot_price: number;
  last_price: number;
  previous_close_price: number;
  volume: number;
  previous_volume: number;
  oi: number;
  previous_oi: number;
  top_bid_price: number;
  top_ask_price: number;
  top_bid_quantity: number;
  top_ask_quantity: number;
  iv: number;
  delta: number;
  gamma: number;
  theta: number;
  vega: number;
}
```

## Common Queries

### 1. Fetch Latest Option Chain

```typescript
const fetchLatestOptionChain = async (expiryDate: string) => {
  const { data, error } = await supabase
    .from('option_chain_view')
    .select('*')
    .eq('expiry_date', expiryDate)
    .order('strike_price', { ascending: true });

  if (error) throw error;
  return data;
};
```

### 2. Get Available Expiry Dates

```typescript
const getExpiryDates = async () => {
  const { data, error } = await supabase
    .from('nifty')
    .select('expiry_date')
    .distinct()
    .order('expiry_date', { ascending: true });

  if (error) throw error;
  return data.map(row => row.expiry_date);
};
```

### 3. Fetch Historical IV Data

```typescript
const fetchHistoricalIV = async (expiryDate: string, strikePrice: number) => {
  const { data, error } = await supabase
    .from('historical_iv_view')
    .select('*')
    .eq('expiry_date', expiryDate)
    .eq('strike_price', strikePrice)
    .order('time_bucket', { ascending: true });

  if (error) throw error;
  return data;
};
```

### 4. Get OI Analysis

```typescript
const fetchOIAnalysis = async (expiryDate: string) => {
  const { data, error } = await supabase
    .from('oi_analysis_view')
    .select('*')
    .eq('expiry_date', expiryDate)
    .order('strike_price', { ascending: true });

  if (error) throw error;
  return data;
};
```

## Visualization Examples

### 1. Option Chain Table

```typescript
import { DataGrid } from '@mui/x-data-grid';

const OptionChainTable = ({ expiryDate }) => {
  const [data, setData] = useState([]);

  const columns = [
    { field: 'strike_price', headerName: 'Strike', width: 100 },
    { field: 'call_price', headerName: 'CE Price', width: 100 },
    { field: 'call_oi', headerName: 'CE OI', width: 100 },
    { field: 'call_volume', headerName: 'CE Volume', width: 100 },
    { field: 'call_iv', headerName: 'CE IV', width: 100 },
    { field: 'put_price', headerName: 'PE Price', width: 100 },
    { field: 'put_oi', headerName: 'PE OI', width: 100 },
    { field: 'put_volume', headerName: 'PE Volume', width: 100 },
    { field: 'put_iv', headerName: 'PE IV', width: 100 },
  ];

  useEffect(() => {
    const fetchData = async () => {
      const chainData = await fetchLatestOptionChain(expiryDate);
      setData(chainData);
    };
    fetchData();
  }, [expiryDate]);

  return (
    <div style={{ height: 600, width: '100%' }}>
      <DataGrid
        rows={data}
        columns={columns}
        pageSize={10}
        rowsPerPageOptions={[10]}
        getRowId={(row) => row.strike_price}
      />
    </div>
  );
};
```

### 2. IV Smile Chart

```typescript
import { Line } from 'react-chartjs-2';

const IVSmileChart = ({ expiryDate }) => {
  const [data, setData] = useState({
    labels: [],
    datasets: []
  });

  useEffect(() => {
    const fetchData = async () => {
      const { data, error } = await supabase
        .from('option_chain_view')
        .select('strike_price,call_iv,put_iv')
        .eq('expiry_date', expiryDate)
        .order('strike_price');

      if (error) return;

      setData({
        labels: data.map(row => row.strike_price),
        datasets: [
          {
            label: 'Call IV',
            data: data.map(row => row.call_iv),
            borderColor: 'rgb(75, 192, 192)',
            tension: 0.1
          },
          {
            label: 'Put IV',
            data: data.map(row => row.put_iv),
            borderColor: 'rgb(255, 99, 132)',
            tension: 0.1
          }
        ]
      });
    };
    fetchData();
  }, [expiryDate]);

  return <Line data={data} />;
};
```

### 3. OI Change Heatmap

```typescript
import { HeatMap } from '@nivo/heatmap';

const OIChangeHeatmap = ({ expiryDate }) => {
  const [data, setData] = useState([]);

  useEffect(() => {
    const fetchData = async () => {
      const oiData = await fetchOIAnalysis(expiryDate);
      setData(oiData);
    };
    fetchData();
  }, [expiryDate]);

  return (
    <div style={{ height: 400 }}>
      <HeatMap
        data={data}
        keys={['oi_change']}
        indexBy="strike_price"
        margin={{ top: 60, right: 60, bottom: 60, left: 60 }}
        colors={{
          type: 'diverging',
          scheme: 'red_yellow_blue'
        }}
      />
    </div>
  );
};
```

## Real-time Updates

### Subscribe to Changes

```typescript
const subscribeToOptionChain = (expiryDate: string, callback: (data: any) => void) => {
  const subscription = supabase
    .from('option_chain_view')
    .on('*', payload => {
      callback(payload.new);
    })
    .eq('expiry_date', expiryDate)
    .subscribe();

  return () => {
    supabase.removeSubscription(subscription);
  };
};
```

## Error Handling

```typescript
const handleSupabaseError = (error: any) => {
  if (error.code === 'PGRST301') {
    console.error('Database connection error');
    // Handle connection error
  } else if (error.code === 'PGRST204') {
    console.error('No data found');
    // Handle no data
  } else {
    console.error('Unknown error:', error);
    // Handle other errors
  }
};

// Usage example
try {
  const data = await fetchLatestOptionChain(expiryDate);
  // Process data
} catch (error) {
  handleSupabaseError(error);
}
```

## Best Practices

1. **Data Caching**
   - Implement client-side caching for frequently accessed data
   - Use React Query or SWR for efficient data fetching and caching

2. **Performance Optimization**
   - Implement pagination for large datasets
   - Use debouncing for real-time updates
   - Optimize re-renders using React.memo and useMemo

3. **Error Boundaries**
   - Implement React Error Boundaries for graceful error handling
   - Show appropriate error messages to users

4. **Loading States**
   - Show loading indicators during data fetching
   - Implement skeleton screens for better UX

## Example Components

### Option Chain Dashboard

```typescript
import React, { useState, useEffect } from 'react';
import { Box, Tab, Tabs, Typography } from '@mui/material';
import { OptionChainTable } from './OptionChainTable';
import { IVSmileChart } from './IVSmileChart';
import { OIChangeHeatmap } from './OIChangeHeatmap';

const OptionChainDashboard = () => {
  const [expiryDate, setExpiryDate] = useState('');
  const [expiryDates, setExpiryDates] = useState([]);
  const [activeTab, setActiveTab] = useState(0);

  useEffect(() => {
    const loadExpiryDates = async () => {
      const dates = await getExpiryDates();
      setExpiryDates(dates);
      if (dates.length > 0) setExpiryDate(dates[0]);
    };
    loadExpiryDates();
  }, []);

  return (
    <Box sx={{ width: '100%' }}>
      <Typography variant="h4" gutterBottom>
        Option Chain Analysis
      </Typography>
      
      <Tabs value={activeTab} onChange={(_, newValue) => setActiveTab(newValue)}>
        <Tab label="Option Chain" />
        <Tab label="IV Analysis" />
        <Tab label="OI Analysis" />
      </Tabs>

      <Box sx={{ mt: 2 }}>
        {activeTab === 0 && <OptionChainTable expiryDate={expiryDate} />}
        {activeTab === 1 && <IVSmileChart expiryDate={expiryDate} />}
        {activeTab === 2 && <OIChangeHeatmap expiryDate={expiryDate} />}
      </Box>
    </Box>
  );
};

export default OptionChainDashboard;
```

For more examples and detailed documentation, refer to:
- [Supabase Documentation](https://supabase.io/docs)
- [Chart.js Documentation](https://www.chartjs.org/docs/latest/)
- [Material-UI Documentation](https://mui.com/) 