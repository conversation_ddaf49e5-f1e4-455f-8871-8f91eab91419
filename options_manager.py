"""
Options Manager

This module manages the collection and storage of options chain data for multiple assets.
It handles scheduling, data fetching, and database operations with admin controls.
"""

import os
import json
import logging
import time
import schedule
import signal
import sys
from datetime import datetime
import pytz
from typing import Dict, List, Any, Optional, Union
import concurrent.futures
import threading

import pandas as pd
from dotenv import load_dotenv
from dhanhq import dhanhq
from supabase import create_client, Client

# Import admin API for asset and settings management
from admin_api import AdminAPI

# Load environment variables
load_dotenv()

# Initialize logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("options_data.log")
    ]
)
logger = logging.getLogger(__name__)

# DhanHQ credentials from environment variables
ACCESS_TOKEN = os.getenv('DHAN_ACCESS_TOKEN')
CLIENT_ID = os.getenv('DHAN_CLIENT_ID')

# Supabase credentials
SUPABASE_URL = os.getenv('SUPABASE_URL')
SUPABASE_KEY = os.getenv('SUPABASE_KEY')

# Validate credentials
if not ACCESS_TOKEN or not CLIENT_ID:
    raise ValueError("Please set DHAN_ACCESS_TOKEN and DHAN_CLIENT_ID in your .env file")

if not SUPABASE_URL or not SUPABASE_KEY:
    raise ValueError("Please set SUPABASE_URL and SUPABASE_KEY in your .env file")

# Initialize DhanHQ client
dhan = dhanhq(
    client_id=CLIENT_ID,
    access_token=ACCESS_TOKEN
)

# Initialize Supabase client
supabase: Client = create_client(
    SUPABASE_URL,
    SUPABASE_KEY
)

# Create necessary directories
os.makedirs('option_data', exist_ok=True)
os.makedirs('raw_data', exist_ok=True)

# Global state for tracking tasks and rate limiting
state = {
    'last_run_time': {},  # Last run time by asset
    'consecutive_errors': {},  # Error count by asset
    'running_tasks': {},  # Currently running tasks
    'shutting_down': False  # Flag to indicate shutdown in progress
}

class OptionsManager:
    """Manager for options chain data collection across multiple assets"""

    @staticmethod
    def get_settings():
        """Get current settings from database"""
        try:
            # Get settings
            option_chain_settings = AdminAPI.get_settings('option_chain_settings')
            admin_settings = AdminAPI.get_settings('admin_settings')
            db_config = AdminAPI.get_settings('database_config')
            
            # Extract values from response
            if isinstance(option_chain_settings, dict) and 'setting_value' in option_chain_settings:
                option_chain_settings = option_chain_settings['setting_value']
                
            if isinstance(admin_settings, dict) and 'setting_value' in admin_settings:
                admin_settings = admin_settings['setting_value']
                
            if isinstance(db_config, dict) and 'setting_value' in db_config:
                db_config = db_config['setting_value']
            
            return {
                'option_chain_settings': option_chain_settings,
                'admin_settings': admin_settings,
                'database_config': db_config
            }
        except Exception as e:
            logger.error(f"Error getting settings: {e}")
            # Fall back to defaults from config file
            from option_chain_config import OPTION_CHAIN_SETTINGS, ADMIN_SETTINGS, DATABASE_CONFIG
            return {
                'option_chain_settings': OPTION_CHAIN_SETTINGS,
                'admin_settings': ADMIN_SETTINGS,
                'database_config': DATABASE_CONFIG
            }

    @staticmethod
    def get_expiry_dates(asset):
        """
        Fetch all available expiry dates for an asset
        
        Args:
            asset: Asset information dictionary
            
        Returns:
            List of expiry dates
        """
        try:
            logger.info(f"Fetching expiry dates for {asset['asset_code']} with security_id: {asset['security_id']}")
            
            response = dhan.expiry_list(
                under_security_id=asset['security_id'],
                under_exchange_segment=asset['exchange_segment']
            )
            
            if (isinstance(response, dict) and 
                response.get('status') == 'success' and 
                isinstance(response.get('data'), dict) and
                isinstance(response['data'].get('data'), list)):
                
                expiry_dates = response['data']['data']
                logger.info(f"Found {len(expiry_dates)} expiry dates for {asset['asset_code']}")
                return expiry_dates
            
            # Check for alternative response formats
            elif isinstance(response, dict) and response.get('status') == 'success' and isinstance(response.get('data'), list):
                expiry_dates = response['data']
                logger.info(f"Found {len(expiry_dates)} expiry dates for {asset['asset_code']} (direct list format)")
                return expiry_dates
            
            elif isinstance(response, dict) and response.get('data') and isinstance(response.get('data'), dict):
                # Try to identify where expiry dates might be stored
                for key, value in response['data'].items():
                    if isinstance(value, list) and value:
                        logger.info(f"Potential expiry dates found in key '{key}' with {len(value)} items")
                        return value
                        
                logger.error(f"Error fetching expiry dates: Found data object but no valid list of dates")
                return []
            else:
                logger.error(f"Error fetching expiry dates: Unexpected response format")
                if isinstance(response, dict):
                    logger.error(f"Response keys: {list(response.keys())}")
                return []
        except Exception as e:
            logger.error(f"Error fetching expiry dates for {asset['asset_code']}: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return []

    @staticmethod
    def store_expiry_dates(asset, expiry_dates):
        """
        Store expiry dates in the database
        
        Args:
            asset: Asset information dictionary
            expiry_dates: List of expiry dates
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Convert expiry dates to records
            records = [
                {
                    'expiry_date': date,
                    'asset_code': asset['asset_code'],
                    'updated_at': datetime.now(pytz.timezone('Asia/Kolkata')).isoformat()
                }
                for date in expiry_dates
            ]
            
            # Upsert the records (insert if not exists, update if exists)
            response = supabase.table('expiry_dates').upsert(
                records,
                on_conflict='expiry_date,asset_code'  # Composite unique constraint
            ).execute()
            
            logger.info(f"Successfully stored/updated {len(records)} expiry dates for {asset['asset_code']}")
            return True
        except Exception as e:
            logger.error(f"Error storing expiry dates for {asset['asset_code']}: {e}")
            return False

    @staticmethod
    def get_stored_expiry_dates(asset_code):
        """
        Get previously stored expiry dates for an asset
        
        Args:
            asset_code: Asset code to get expiry dates for
            
        Returns:
            List of expiry dates
        """
        try:
            response = supabase.table('expiry_dates')\
                .select('expiry_date')\
                .eq('asset_code', asset_code)\
                .execute()
            
            if response.data:
                return [record['expiry_date'] for record in response.data]
            return []
        except Exception as e:
            logger.error(f"Error fetching stored expiry dates for {asset_code}: {e}")
            return []

    @staticmethod
    def fetch_options_chain_with_retry(asset, expiry_date, max_retries=3, retry_delay=5):
        """
        Fetch options chain data with retry logic for handling API errors
        
        Args:
            asset: Asset information dictionary
            expiry_date: Expiry date to fetch data for
            max_retries: Maximum number of retry attempts
            retry_delay: Delay between retries in seconds
            
        Returns:
            Options chain data or error response
        """
        asset_code = asset['asset_code']
        for attempt in range(max_retries):
            try:
                logger.info(f"Fetching options chain for {asset_code} expiry: {expiry_date} (Attempt {attempt+1}/{max_retries})")
                options_chain = dhan.option_chain(
                    under_security_id=asset['security_id'],
                    under_exchange_segment=asset['exchange_segment'],
                    expiry=expiry_date
                )
                
                # Check if we got a valid response
                if isinstance(options_chain, dict) and options_chain.get('status') == 'success':
                    logger.info(f"Successfully fetched options chain for {asset_code} expiry {expiry_date}")
                    return options_chain
                
                # If we got a failure response with empty data or JSON parse error
                if isinstance(options_chain, dict) and options_chain.get('status') == 'failure':
                    logger.warning(f"API returned failure for {asset_code} {expiry_date}: {options_chain.get('remarks', 'No error details')}")
                    
                    # If it's a JSON parsing error, wait and retry
                    if 'Expecting value' in str(options_chain.get('remarks', '')):
                        logger.info(f"JSON parsing error detected. Retrying in {retry_delay} seconds...")
                        time.sleep(retry_delay)
                        continue
                
                logger.warning(f"Unexpected response format for {asset_code}: {options_chain}")
                return options_chain
                
            except Exception as e:
                logger.error(f"Error fetching options chain for {asset_code} {expiry_date} (Attempt {attempt+1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    logger.info(f"Retrying in {retry_delay} seconds...")
                    time.sleep(retry_delay)
                else:
                    logger.error(f"Maximum retry attempts reached for {asset_code} {expiry_date}")
                    return {"status": "failure", "remarks": f"Maximum retry attempts reached: {str(e)}", "data": ""}

        return {"status": "failure", "remarks": "All retry attempts failed", "data": ""}

    @staticmethod
    def process_options_data(asset, options_chain, expiry_date, db_config):
        """
        Process options chain data and save to Supabase
        
        Args:
            asset: Asset information dictionary
            options_chain: Options chain data from API
            expiry_date: Expiry date of the options
            db_config: Database configuration settings
            
        Returns:
            True if successful, False otherwise
        """
        asset_code = asset['asset_code']
        try:
            if not options_chain or not isinstance(options_chain, dict):
                logger.warning(f"No valid data available for {asset_code} expiry {expiry_date}")
                return False
            
            # The response has a nested structure
            if not (options_chain.get('status') == 'success' and 
                    isinstance(options_chain.get('data'), dict) and
                    isinstance(options_chain['data'].get('data'), dict)):
                logger.error(f"Invalid response format for {asset_code} expiry {expiry_date}")
                return False

            options_data = options_chain['data']['data']
            spot_price = options_data.get('last_price', 0)
            timestamp = datetime.now(pytz.timezone('Asia/Kolkata')).isoformat()
            
            # Store complete raw response
            if db_config.get('store_raw_data', True):
                try:
                    raw_table_name = f"{db_config.get('raw_data_table_prefix', 'raw_')}{asset_code.lower()}"
                    raw_record = {
                        'expiry_date': expiry_date,
                        'asset_code': asset_code,
                        'raw_data': options_chain,
                        'spot_price': float(spot_price) if spot_price else 0
                    }
                    supabase.table(raw_table_name).insert(raw_record).execute()
                    logger.info(f"Successfully stored complete options chain data for {asset_code} expiry {expiry_date}")
                except Exception as e:
                    logger.error(f"Error storing raw options data for {asset_code}: {e}")
            
            # Lists to store options data
            records = []
            
            # Process option chain data
            oc_data = options_data.get('oc', {})
            for strike_key, strike_data in oc_data.items():
                try:
                    strike_price = float(strike_key.replace('.000000', ''))
                except (ValueError, AttributeError):
                    continue
                
                # Process call and put options
                for option_type in ['ce', 'pe']:
                    option_data = strike_data.get(option_type, {})
                    if option_data:
                        option = {
                            'timestamp': timestamp,
                            'expiry_date': expiry_date,
                            'strike_price': strike_price,
                            'option_type': option_type.upper(),
                            'spot_price': float(spot_price) if spot_price else 0,
                            'last_price': float(option_data.get('last_price', 0) or 0),
                            'previous_close_price': float(option_data.get('previous_close_price', 0) or 0),
                            'volume': int(option_data.get('volume', 0) or 0),
                            'previous_volume': int(option_data.get('previous_volume', 0) or 0),
                            'oi': int(option_data.get('oi', 0) or 0),
                            'previous_oi': int(option_data.get('previous_oi', 0) or 0),
                            'top_bid_price': float(option_data.get('top_bid_price', 0) or 0),
                            'top_ask_price': float(option_data.get('top_ask_price', 0) or 0),
                            'top_bid_quantity': int(option_data.get('top_bid_quantity', 0) or 0),
                            'top_ask_quantity': int(option_data.get('top_ask_quantity', 0) or 0),
                            'iv': float(option_data.get('implied_volatility', 0) or 0),
                            'asset_code': asset_code
                        } 
                        
                        # Add greeks if available
                        greeks = option_data.get('greeks', {})
                        if greeks:
                            option.update({
                                'delta': float(greeks.get('delta', 0) or 0),
                                'gamma': float(greeks.get('gamma', 0) or 0),
                                'theta': float(greeks.get('theta', 0) or 0),
                                'vega': float(greeks.get('vega', 0) or 0)
                            })
                        else:
                            option.update({
                                'delta': 0.0,
                                'gamma': 0.0,
                                'theta': 0.0,
                                'vega': 0.0
                            })
                        
                        records.append(option)
            
            if records:
                try:
                    # Get table name for this asset
                    options_table_name = f"{db_config.get('options_table_prefix', 'options_')}{asset_code.lower()}"
                    
                    # Save to Supabase in batches
                    batch_size = db_config.get('batch_size', 1000)
                    for i in range(0, len(records), batch_size):
                        batch = records[i:i + batch_size]
                        supabase.table(options_table_name).insert(batch).execute()
                        logger.info(f"Successfully stored batch of {len(batch)} records for {asset_code} expiry {expiry_date}")
                    
                    # Calculate ATM strike
                    atm_strike = min(records, key=lambda x: abs(x['strike_price'] - spot_price))['strike_price']
                    
                    # Log summary
                    logger.info(f"\nOptions Chain Summary for {asset_code} {expiry_date}:")
                    logger.info(f"Spot Price: {spot_price}")
                    logger.info(f"Total Strikes: {len(oc_data)}")
                    logger.info(f"Total Options: {len(records)}")
                    logger.info(f"ATM Strike: {atm_strike}")
                    
                    # Save to CSV as backup if enabled
                    if db_config.get('backup_enabled', True):
                        df = pd.DataFrame(records)
                        csv_file = f"option_data/{asset_code.lower()}_options_{expiry_date.replace('-', '')}.csv"
                        df.to_csv(csv_file, index=False)
                    
                    return True
                    
                except Exception as e:
                    logger.error(f"Error uploading to Supabase for {asset_code}: {e}")
                    # Save to CSV as backup
                    df = pd.DataFrame(records)
                    csv_file = f"option_data/{asset_code.lower()}_options_{expiry_date.replace('-', '')}.csv"
                    df.to_csv(csv_file, index=False)
                    logger.info(f"Backup saved to {csv_file}")
                    return False
            else:
                logger.warning(f"No valid options data found for {asset_code} {expiry_date}")
                return False
                
        except Exception as e:
            logger.error(f"Error processing options data for {asset_code} {expiry_date}: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return False

    @staticmethod
    def fetch_asset_options(asset):
        """
        Fetch and store options data for a single asset
        
        Args:
            asset: Asset information dictionary
            
        Returns:
            Success status and summary
        """
        asset_code = asset['asset_code']
        logger.info(f"\n\n--- Processing asset: {asset_code} ---")
        
        try:
            # Get settings
            settings = OptionsManager.get_settings()
            option_chain_settings = settings['option_chain_settings']
            db_config = settings['database_config']
            
            # Update state for this asset
            if asset_code not in state['last_run_time']:
                state['last_run_time'][asset_code] = 0
            if asset_code not in state['consecutive_errors']:
                state['consecutive_errors'][asset_code] = 0
            
            # Fetch expiry dates
            expiry_dates = OptionsManager.get_expiry_dates(asset)
            if not expiry_dates:
                logger.error(f"Could not fetch expiry dates for {asset_code}")
                state['consecutive_errors'][asset_code] += 1
                return {
                    'asset_code': asset_code,
                    'success': False,
                    'error': "Failed to fetch expiry dates"
                }

            # Get previously stored expiry dates
            stored_dates = OptionsManager.get_stored_expiry_dates(asset_code)
            
            # Check for new dates
            if set(expiry_dates) != set(stored_dates):
                logger.info(f"Found changes in expiry dates for {asset_code}. Updating database...")
                OptionsManager.store_expiry_dates(asset, expiry_dates)
            else:
                logger.info(f"No changes in expiry dates for {asset_code}")
            
            logger.info(f"\nAvailable Expiry Dates for {asset_code}:")
            for date in expiry_dates:
                logger.info(date)
            
            # Limit the number of expiries to fetch
            max_expiries = option_chain_settings.get('max_expiries_to_fetch', 5)
            if len(expiry_dates) > max_expiries:
                logger.info(f"Limiting to {max_expiries} expiry dates for {asset_code}")
                expiry_dates = expiry_dates[:max_expiries]

            # Fetch options chain for each expiry date
            success_count = 0
            for expiry_date in expiry_dates:
                try:
                    # Use the retry function instead of direct API call
                    options_chain = OptionsManager.fetch_options_chain_with_retry(asset, expiry_date)
                    
                    # Save raw response to file (for backup)
                    if db_config.get('backup_enabled', True):
                        raw_file = f"raw_data/{asset_code.lower()}_options_raw_{expiry_date.replace('-', '')}.json"
                        with open(raw_file, 'w') as f:
                            json.dump(options_chain, f, indent=2)
                    
                    # Only process if we got a success response
                    if options_chain.get('status') == 'success':
                        if OptionsManager.process_options_data(asset, options_chain, expiry_date, db_config):
                            success_count += 1
                    else:
                        logger.warning(f"Skipping processing for {asset_code} {expiry_date} due to API error")
                        
                except Exception as e:
                    logger.error(f"Error fetching options chain for {asset_code} {expiry_date}: {e}")
                
                # Add a small delay to avoid rate limiting
                time.sleep(1)
            
            # Update state for successful run
            state['last_run_time'][asset_code] = time.time()
            state['consecutive_errors'][asset_code] = 0
            
            logger.info(f"\nCompleted processing {success_count}/{len(expiry_dates)} expiry dates for {asset_code}")
            return {
                'asset_code': asset_code,
                'success': success_count > 0,
                'expiries_processed': success_count,
                'total_expiries': len(expiry_dates)
            }
            
        except Exception as e:
            logger.error(f"Error processing asset {asset_code}: {e}")
            import traceback
            logger.error(traceback.format_exc())
            
            # Update error state
            state['consecutive_errors'][asset_code] += 1
            
            return {
                'asset_code': asset_code,
                'success': False,
                'error': str(e)
            }
        finally:
            # Clear running task flag
            if asset_code in state['running_tasks']:
                del state['running_tasks'][asset_code]

    @staticmethod
    def fetch_all_options_data():
        """
        Fetch and store options data for all active assets
        
        Returns:
            List of results by asset
        """
        logger.info(f"\n\n--- Running data collection: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} ---")
        
        try:
            # Get active assets
            assets = AdminAPI.get_assets(active_only=True)
            
            if not assets:
                logger.error("No active assets found. Check asset configuration.")
                return []
                
            # Get settings
            settings = OptionsManager.get_settings()
            admin_settings = settings['admin_settings']
            
            # Determine how many assets to process concurrently
            max_concurrent = admin_settings.get('max_concurrent_assets', 1)
            
            results = []
            
            # If only one asset can run at a time, run them sequentially
            if max_concurrent <= 1:
                for asset in assets:
                    if state['shutting_down']:
                        break
                        
                    # Mark task as running
                    state['running_tasks'][asset['asset_code']] = True
                    
                    # Process asset
                    result = OptionsManager.fetch_asset_options(asset)
                    results.append(result)
            else:
                # Process assets concurrently
                with concurrent.futures.ThreadPoolExecutor(max_workers=max_concurrent) as executor:
                    # Submit all tasks
                    future_to_asset = {}
                    for asset in assets:
                        if state['shutting_down']:
                            break
                            
                        # Mark task as running
                        state['running_tasks'][asset['asset_code']] = True
                        
                        # Submit task
                        future = executor.submit(OptionsManager.fetch_asset_options, asset)
                        future_to_asset[future] = asset
                    
                    # Get results as they complete
                    for future in concurrent.futures.as_completed(future_to_asset):
                        asset = future_to_asset[future]
                        try:
                            result = future.result()
                            results.append(result)
                        except Exception as e:
                            logger.error(f"Error processing asset {asset['asset_code']}: {e}")
                            results.append({
                                'asset_code': asset['asset_code'],
                                'success': False,
                                'error': str(e)
                            })
            
            logger.info("\nAll assets have been processed")
            return results
            
        except Exception as e:
            logger.error(f"Error in data collection process: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return []
        finally:
            logger.info(f"--- Completed data collection: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} ---\n")

    @staticmethod
    def setup_schedule():
        """
        Setup scheduled execution of data fetching
        
        Returns:
            None
        """
        settings = OptionsManager.get_settings()
        refresh_interval = settings['option_chain_settings'].get('data_refresh_interval_seconds', 5)
        
        logger.info(f"Setting up scheduled execution every {refresh_interval} seconds")
        
        def rate_limited_fetch():
            """Rate-limited fetch function for scheduler"""
            if state['shutting_down']:
                return
                
            try:
                OptionsManager.fetch_all_options_data()
            except Exception as e:
                logger.error(f"Error in scheduled execution: {e}")
        
        # Run once immediately
        rate_limited_fetch()
        
        # Schedule to run at the specified interval
        schedule.every(refresh_interval).seconds.do(rate_limited_fetch)
        logger.info(f"Scheduled to run every {refresh_interval} seconds")
        
        # Setup signal handling for graceful shutdown
        def signal_handler(sig, frame):
            logger.info("\nReceived shutdown signal. Shutting down gracefully...")
            state['shutting_down'] = True
            
            # Wait for running tasks to complete
            if state['running_tasks']:
                logger.info(f"Waiting for {len(state['running_tasks'])} running tasks to complete...")
                
                # Wait up to 30 seconds for tasks to complete
                for i in range(30):
                    if not state['running_tasks']:
                        break
                    time.sleep(1)
            
            logger.info("Shutdown complete.")
            sys.exit(0)
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
        # Keep running indefinitely
        logger.info("Scheduler is running. Press Ctrl+C to exit.")
        try:
            while not state['shutting_down']:
                schedule.run_pending()
                time.sleep(0.1)  # Small sleep to prevent CPU overuse
        except Exception as e:
            logger.error(f"Error in scheduler: {str(e)}")
            sys.exit(1)

    @staticmethod
    def is_trading_hours():
        """
        Check if current time is within trading hours
        
        Returns:
            Boolean indicating if within trading hours
        """
        # Get settings
        settings = OptionsManager.get_settings()
        option_chain_settings = settings['option_chain_settings']
        
        # Get market hours from settings
        market_start = option_chain_settings.get('market_start_time', '09:15')
        market_end = option_chain_settings.get('market_end_time', '15:30')
        
        # Get current time in IST
        ist = pytz.timezone('Asia/Kolkata')
        current_time = datetime.now(ist).strftime('%H:%M')
        
        # Check if within trading hours
        return market_start <= current_time <= market_end


# Execute directly if run as a script
if __name__ == "__main__":
    # Initialize the database from configuration file
    logger.info("Initializing database from configuration file...")
    result = AdminAPI.initialize_from_config()
    
    if isinstance(result, dict) and 'error' in result:
        logger.error(f"Failed to initialize database: {result['error']}")
    else:
        logger.info("Database initialized successfully")
    
    # Start the scheduler
    OptionsManager.setup_schedule()