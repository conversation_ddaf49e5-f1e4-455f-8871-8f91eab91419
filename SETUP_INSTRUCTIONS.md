# Database Setup Instructions

## 1. Create the SQL Function

First, we need to create a function that allows us to execute SQL commands. Follow these steps:

1. Go to your Supabase project dashboard
2. Navigate to the SQL Editor
3. Create a new query and paste the following SQL:

```sql
-- Enable pgcrypto extension for security features
CREATE EXTENSION IF NOT EXISTS pgcrypto;

-- Drop the function if it exists
DROP FUNCTION IF EXISTS exec_sql;

-- Create the function to execute raw SQL
CREATE OR REPLACE FUNCTION exec_sql(sql_command text)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    EXECUTE sql_command;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION exec_sql TO authenticated;
```

4. Click "Run" to create the function

## 2. Create the Views

After creating the function, you can create the views directly in the SQL Editor:

1. Create a new query in the SQL Editor
2. Copy the contents of `create_option_views.sql` into the editor
3. Click "Run" to create all the views

The views that will be created are:
- `option_chain_detailed_view`: Shows the latest data for each strike price with both CE and PE data side by side
- `option_chain_changes_view`: Tracks changes in option prices and OI over different time intervals
- `option_chain_summary_view`: Provides a summary of option chain data for each expiry
- `intraday_changes_view`: Shows intraday changes for each option

## 3. Verify the Views

You can verify that the views were created successfully by running these test queries in the SQL Editor:

```sql
-- Test option_chain_detailed_view
SELECT COUNT(*) FROM option_chain_detailed_view;

-- Test option_chain_changes_view
SELECT COUNT(*) FROM option_chain_changes_view;

-- Test option_chain_summary_view
SELECT COUNT(*) FROM option_chain_summary_view;

-- Test intraday_changes_view
SELECT COUNT(*) FROM intraday_changes_view;
```

## 4. Example Queries

Here are some example queries you can use to test the views:

```sql
-- Get detailed option chain for specific expiry
SELECT *
FROM option_chain_detailed_view
WHERE expiry_date = '2025-04-09'
ORDER BY strike_diff ASC
LIMIT 20;

-- Get intraday changes for ATM options
SELECT *
FROM intraday_changes_view
WHERE expiry_date = '2025-04-09'
  AND ABS(strike_price - spot_price) < 100
ORDER BY strike_price;

-- Get option chain summary
SELECT *
FROM option_chain_summary_view
ORDER BY expiry_date;

-- Get significant OI changes
SELECT *
FROM option_chain_changes_view
WHERE ABS(oi_change) > 100000
  AND minutes_since_last_update < 60
ORDER BY timestamp DESC;
```

## 5. Troubleshooting

If you encounter any issues:

1. Check that the `nifty` table exists and has data
2. Verify that all required columns are present in the `nifty` table
3. Make sure you have the necessary permissions to create views
4. Check the Supabase logs for any error messages

## 6. Maintenance

The views will automatically update as new data is added to the `nifty` table. No additional maintenance is required unless you need to modify the view definitions. 